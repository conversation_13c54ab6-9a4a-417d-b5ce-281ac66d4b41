import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../supabase/client.js';

// For debugging
console.log('Supabase client in CompanyProfile:', supabase);

function CompanyProfile() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [companyProfile, setCompanyProfile] = useState(() => {
    const savedProfile = localStorage.getItem('companyProfile');
    return savedProfile ? JSON.parse(savedProfile) : {
      name: '',
      industry: '',
      size: '',
      founded: '',
      location: '',
      mission: '',
      vision: '',
      values: '',
      targetAudience: '',
      uniqueSellingPoints: '',
      competitors: '',
      challenges: '',
      goals: ''
    };
  });
  
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Fetch company profile from Supabase when component mounts
  useEffect(() => {
    if (user) {
      fetchCompanyProfile();
    }
  }, [user]);
  
  const fetchCompanyProfile = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const { data, error } = await supabase
        .from('company_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is the error code for no rows returned
        console.error('Error fetching company profile:', error);
        setError('Failed to load company profile');
      }
      
      if (data) {
        // Map database fields to local state
        const mappedProfile = {
          name: data.company_name || '',
          industry: data.industry || '',
          size: data.company_size || '',
          founded: data.founded_year ? data.founded_year.toString() : '',
          location: data.location || '',
          mission: data.mission || '',
          vision: data.vision || '',
          values: data.core_values || '',
          targetAudience: data.target_audience || '',
          uniqueSellingPoints: data.unique_selling_points || '',
          competitors: data.competitors || '',
          challenges: data.challenges || '',
          goals: data.business_goals || ''
        };
        
        setCompanyProfile(mappedProfile);
        
        // Also update localStorage
        localStorage.setItem('companyProfile', JSON.stringify(mappedProfile));
      }
    } catch (err) {
      console.error('Exception when fetching company profile:', err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCompanyProfile(prev => ({
      ...prev,
      [name]: value
    }));
    setIsSaved(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setIsSaved(false);
    
    try {
      // Save to localStorage
      localStorage.setItem('companyProfile', JSON.stringify(companyProfile));
      
      if (user) {
        // Map local state to database fields
        const dbProfile = {
          user_id: user.id,
          company_name: companyProfile.name,
          industry: companyProfile.industry,
          company_size: companyProfile.size,
          founded_year: companyProfile.founded ? parseInt(companyProfile.founded) : null,
          location: companyProfile.location,
          mission: companyProfile.mission,
          vision: companyProfile.vision,
          core_values: companyProfile.values,
          target_audience: companyProfile.targetAudience,
          unique_selling_points: companyProfile.uniqueSellingPoints,
          competitors: companyProfile.competitors,
          challenges: companyProfile.challenges,
          business_goals: companyProfile.goals
        };
        
        // Check if profile already exists
        const { data: existingProfile } = await supabase
          .from('company_profiles')
          .select('id')
          .eq('user_id', user.id)
          .single();
        
        let result;
        if (existingProfile) {
          // Update existing profile
          result = await supabase
            .from('company_profiles')
            .update(dbProfile)
            .eq('user_id', user.id);
        } else {
          // Insert new profile
          result = await supabase
            .from('company_profiles')
            .insert([dbProfile]);
        }
        
        if (result.error) {
          throw result.error;
        }
        
        console.log('Company profile saved to database');
      }
      
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 3000);
    } catch (err) {
      console.error('Error saving company profile:', err);
      setError('Failed to save company profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md">
      <h2 className="raleway-title-h2 mb-6 text-center">Company Profile</h2>
      <p className="body-text mb-6 text-center">
        Complete your company profile to help our AI generate more precise and tailored marketing strategies.
        This information will be included in all prompt contexts.
      </p>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
          {error}
        </div>
      )}
      
      {isLoading && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-center">
          Loading...
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-gray-700 font-semibold mb-2">Company Name</label>
            <input
              type="text"
              name="name"
              value={companyProfile.name}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Acme Corporation"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">Industry</label>
            <input
              type="text"
              name="industry"
              value={companyProfile.industry}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Technology, Healthcare, Retail"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">Company Size</label>
            <select
              name="size"
              value={companyProfile.size}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select company size</option>
              <option value="1-10">1-10 employees</option>
              <option value="11-50">11-50 employees</option>
              <option value="51-200">51-200 employees</option>
              <option value="201-500">201-500 employees</option>
              <option value="501-1000">501-1000 employees</option>
              <option value="1000+">1000+ employees</option>
            </select>
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">Year Founded</label>
            <input
              type="text"
              name="founded"
              value={companyProfile.founded}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., 2010"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">Location</label>
            <input
              type="text"
              name="location"
              value={companyProfile.location}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., New York, USA"
            />
          </div>
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Mission Statement</label>
          <textarea
            name="mission"
            value={companyProfile.mission}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="Your company's mission statement"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Vision</label>
          <textarea
            name="vision"
            value={companyProfile.vision}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="Your company's vision for the future"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Core Values</label>
          <textarea
            name="values"
            value={companyProfile.values}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="List your company's core values"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Target Audience</label>
          <textarea
            name="targetAudience"
            value={companyProfile.targetAudience}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="Describe your ideal customers or clients"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Unique Selling Points</label>
          <textarea
            name="uniqueSellingPoints"
            value={companyProfile.uniqueSellingPoints}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="What makes your company different from competitors?"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Main Competitors</label>
          <textarea
            name="competitors"
            value={companyProfile.competitors}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="List your main competitors"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Current Challenges</label>
          <textarea
            name="challenges"
            value={companyProfile.challenges}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="What challenges is your company currently facing?"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-semibold mb-2">Business Goals</label>
          <textarea
            name="goals"
            value={companyProfile.goals}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24"
            placeholder="What are your short-term and long-term business goals?"
          />
        </div>
        
        <div className="flex justify-center">
          <button
            type="submit"
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Company Profile'}
          </button>
        </div>
        
        {isSaved && (
          <div className="text-center text-green-600 font-semibold">
            Company profile saved successfully to both local storage and database!
          </div>
        )}
      </form>
    </div>
  );
}

export default CompanyProfile;
