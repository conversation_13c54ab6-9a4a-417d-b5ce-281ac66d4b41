import { supabase } from '../supabase/client';

/**
 * User Profile Service
 * Manages user profiles, roles, and permissions
 */

export class UserProfileService {
  /**
   * Get user profile by user ID
   * @param {string} userId - User ID from auth
   * @returns {Object|null} User profile or null if not found
   */
  static async getUserProfile(userId) {
    try {
      console.log('UserProfileService: Fetching profile for userId:', userId);

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.log('UserProfileService: Error fetching profile:', error.code, error.message);
        if (error.code === 'PGRST116') {
          // No profile found, create one
          console.log('UserProfileService: No profile found, creating new one');
          return await this.createUserProfile(userId);
        }
        throw error;
      }

      console.log('UserProfileService: Profile found:', data);
      return data;
    } catch (error) {
      console.error('UserProfileService: Error in getUserProfile:', error);
      return null;
    }
  }

  /**
   * Create user profile for a new user
   * @param {string} userId - User ID from auth
   * @param {Object} profileData - Additional profile data
   * @returns {Object|null} Created profile or null if failed
   */
  static async createUserProfile(userId, profileData = {}) {
    try {
      // Get user data from auth
      const { data: authUser, error: authError } = await supabase.auth.getUser();
      if (authError) throw authError;

      const user = authUser.user;
      if (!user || user.id !== userId) {
        throw new Error('User not found or ID mismatch');
      }

      const profile = {
        user_id: userId,
        email: user.email,
        full_name: user.user_metadata?.full_name || user.user_metadata?.name || '',
        role: 'user',
        tier: 'free',
        is_active: true,
        ...profileData
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .insert([profile])
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating user profile:', error);
      return null;
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} updates - Profile updates
   * @returns {Object|null} Updated profile or null if failed
   */
  static async updateUserProfile(userId, updates) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return null;
    }
  }

  /**
   * Check if user has admin role
   * @param {string} userId - User ID
   * @returns {boolean} True if user is admin
   */
  static async isAdmin(userId) {
    try {
      const profile = await this.getUserProfile(userId);
      return profile?.role === 'admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Check if user has specific permission
   * @param {string} userId - User ID
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  static async hasPermission(userId, permission) {
    try {
      const profile = await this.getUserProfile(userId);
      if (!profile) return false;

      // Admins have all permissions
      if (profile.role === 'admin') return true;

      // Check specific permission
      const permissions = profile.permissions || {};
      return permissions[permission] === true;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Get all user profiles (admin only)
   * @returns {Array} Array of user profiles
   */
  static async getAllUserProfiles() {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching all user profiles:', error);
      return [];
    }
  }

  /**
   * Update user role (admin only)
   * @param {string} userId - User ID to update
   * @param {string} role - New role (admin, manager, user)
   * @returns {Object|null} Updated profile or null if failed
   */
  static async updateUserRole(userId, role) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          role,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating user role:', error);
      return null;
    }
  }

  /**
   * Update user tier (admin only)
   * @param {string} userId - User ID to update
   * @param {string} tier - New tier (free, basic, premium)
   * @returns {Object|null} Updated profile or null if failed
   */
  static async updateUserTier(userId, tier) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          tier,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating user tier:', error);
      return null;
    }
  }

  /**
   * Update user permissions (admin only)
   * @param {string} userId - User ID to update
   * @param {Object} permissions - New permissions object
   * @returns {Object|null} Updated profile or null if failed
   */
  static async updateUserPermissions(userId, permissions) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          permissions,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating user permissions:', error);
      return null;
    }
  }

  /**
   * Get user statistics (admin only)
   * @returns {Object} User statistics
   */
  static async getUserStatistics() {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('role, tier, is_active');

      if (error) throw error;

      const stats = {
        total: data.length,
        active: data.filter(u => u.is_active).length,
        inactive: data.filter(u => !u.is_active).length,
        byRole: {
          admin: data.filter(u => u.role === 'admin').length,
          manager: data.filter(u => u.role === 'manager').length,
          user: data.filter(u => u.role === 'user').length
        },
        byTier: {
          free: data.filter(u => u.tier === 'free').length,
          basic: data.filter(u => u.tier === 'basic').length,
          premium: data.filter(u => u.tier === 'premium').length
        }
      };

      return stats;
    } catch (error) {
      console.error('Error fetching user statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byRole: { admin: 0, manager: 0, user: 0 },
        byTier: { free: 0, basic: 0, premium: 0 }
      };
    }
  }

  /**
   * Deactivate user account (admin only)
   * @param {string} userId - User ID to deactivate
   * @returns {Object|null} Updated profile or null if failed
   */
  static async deactivateUser(userId) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error deactivating user:', error);
      return null;
    }
  }

  /**
   * Activate user account (admin only)
   * @param {string} userId - User ID to activate
   * @returns {Object|null} Updated profile or null if failed
   */
  static async activateUser(userId) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error activating user:', error);
      return null;
    }
  }
}

export default UserProfileService;
