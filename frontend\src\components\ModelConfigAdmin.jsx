import React, { useState, useEffect } from 'react';
import { ALL_MODELS, CLIENT_MODEL_CONFIGS, getAvailableModels } from '../config/modelConfig';
import { useAuth } from '../context/AuthContext';

/**
 * Admin interface for managing model access configurations
 * This component allows administrators to control which models are available to different clients
 */
export default function ModelConfigAdmin() {
  const { isAdmin } = useAuth();
  const [clientConfigs, setClientConfigs] = useState(CLIENT_MODEL_CONFIGS);
  const [newClientEmail, setNewClientEmail] = useState('');
  const [selectedClient, setSelectedClient] = useState('');
  const [previewClient, setPreviewClient] = useState('');
  const [showAddClient, setShowAddClient] = useState(false);

  if (!isAdmin()) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-xl font-semibold text-red-800 mb-2">Access Denied</h2>
          <p className="text-red-700">You don't have permission to access the model configuration admin panel.</p>
        </div>
      </div>
    );
  }

  const handleAddClient = () => {
    if (newClientEmail && !clientConfigs[newClientEmail]) {
      setClientConfigs({
        ...clientConfigs,
        [newClientEmail]: {}
      });
      setSelectedClient(newClientEmail);
      setNewClientEmail('');
      setShowAddClient(false);
    }
  };

  const handleModelToggle = (clientEmail, modelId) => {
    const updatedConfigs = { ...clientConfigs };
    if (!updatedConfigs[clientEmail]) {
      updatedConfigs[clientEmail] = {};
    }
    
    // Toggle the model access
    const currentValue = updatedConfigs[clientEmail][modelId];
    if (currentValue === undefined) {
      // If not set, default is true, so set to false
      updatedConfigs[clientEmail][modelId] = false;
    } else {
      // Toggle the current value
      updatedConfigs[clientEmail][modelId] = !currentValue;
    }
    
    setClientConfigs(updatedConfigs);
  };

  const handleRemoveClient = (clientEmail) => {
    const updatedConfigs = { ...clientConfigs };
    delete updatedConfigs[clientEmail];
    setClientConfigs(updatedConfigs);
    if (selectedClient === clientEmail) {
      setSelectedClient('');
    }
  };

  const exportConfiguration = () => {
    const configData = JSON.stringify(clientConfigs, null, 2);
    const blob = new Blob([configData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'model-config.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const getModelStatus = (clientEmail, modelId) => {
    const clientConfig = clientConfigs[clientEmail] || {};
    return clientConfig[modelId] !== false; // Default is true unless explicitly set to false
  };

  const previewModels = previewClient ? getAvailableModels(previewClient, { email: previewClient }) : {};

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Model Access Configuration</h1>
        
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Instructions</h3>
          <p className="text-blue-700 text-sm">
            Use this interface to control which AI models are available to different clients. 
            By default, all models are available unless explicitly disabled for a client.
          </p>
        </div>

        {/* Client Management */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-700">Client Configurations</h2>
            <div className="space-x-2">
              <button
                onClick={() => setShowAddClient(true)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
              >
                Add Client
              </button>
              <button
                onClick={exportConfiguration}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              >
                Export Config
              </button>
            </div>
          </div>

          {/* Add Client Form */}
          {showAddClient && (
            <div className="mb-4 p-4 bg-gray-50 border rounded-lg">
              <div className="flex items-center space-x-3">
                <input
                  type="email"
                  value={newClientEmail}
                  onChange={(e) => setNewClientEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded"
                />
                <button
                  onClick={handleAddClient}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
                >
                  Add
                </button>
                <button
                  onClick={() => setShowAddClient(false)}
                  className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {/* Client List */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            {Object.keys(clientConfigs).map(clientEmail => (
              <div key={clientEmail} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-800 truncate">{clientEmail}</h3>
                  <button
                    onClick={() => handleRemoveClient(clientEmail)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  {Object.values(clientConfigs[clientEmail] || {}).filter(v => v === false).length} models disabled
                </div>
                <button
                  onClick={() => setSelectedClient(selectedClient === clientEmail ? '' : clientEmail)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {selectedClient === clientEmail ? 'Hide Details' : 'Configure'}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Model Configuration for Selected Client */}
        {selectedClient && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-4">
              Configure Models for: {selectedClient}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(ALL_MODELS).map(([modelId, modelInfo]) => {
                const isEnabled = getModelStatus(selectedClient, modelId);
                return (
                  <div key={modelId} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{modelInfo.label}</h4>
                        <p className="text-sm text-gray-600">{modelInfo.category}</p>
                        <p className="text-xs text-gray-500">{modelInfo.description}</p>
                      </div>
                      <label className="flex items-center ml-3">
                        <input
                          type="checkbox"
                          checked={isEnabled}
                          onChange={() => handleModelToggle(selectedClient, modelId)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {isEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </label>
                    </div>
                    <div className="text-xs text-gray-500">
                      Cost: {modelInfo.cost} | Provider: {modelInfo.provider}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Model Preview */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Preview Client Access</h2>
          <div className="flex items-center space-x-3 mb-4">
            <input
              type="email"
              value={previewClient}
              onChange={(e) => setPreviewClient(e.target.value)}
              placeholder="Enter client email to preview their available models"
              className="flex-1 px-3 py-2 border border-gray-300 rounded"
            />
          </div>
          
          {previewClient && (
            <div className="bg-gray-50 border rounded-lg p-4">
              <h3 className="font-medium text-gray-800 mb-3">
                Available models for: {previewClient}
              </h3>
              {Object.keys(previewModels).length > 0 ? (
                <div className="space-y-2">
                  {Object.entries(previewModels).map(([category, models]) => (
                    <div key={category}>
                      <h4 className="font-medium text-gray-700">{category}</h4>
                      <ul className="ml-4 text-sm text-gray-600">
                        {models.map(model => (
                          <li key={model.value}>• {model.label}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600">No models available for this client.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
