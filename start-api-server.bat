@echo off
echo Starting Market Research API Server...
echo This window will keep the server running. Do not close it.
echo.

:start_server
echo [%date% %time%] Starting API server...
start /b node stable-api-server.js > api-server-log.txt 2>&1
echo Server started with PID: !ERRORLEVEL!
echo.

echo Server is now running on http://localhost:3001
echo.
echo Press Ctrl+C to stop the server
echo.

timeout /t 60
goto start_server
