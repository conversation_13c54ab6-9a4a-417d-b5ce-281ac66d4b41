// Test script for the stable API server
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// API endpoints to test
const endpoints = [
  {
    name: 'Gemini',
    url: 'http://localhost:3001/api/gemini',
    data: { prompt: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'gemini-1.5-pro' }
  },
  {
    name: 'OpenAI',
    url: 'http://localhost:3001/api/openai',
    data: { question: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'gpt-3.5-turbo' }
  },
  {
    name: 'DeepSeek',
    url: 'http://localhost:3001/api/deepseek',
    data: { prompt: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'deepseek-chat' }
  }
];

// Function to check if server is up
async function checkServerStatus() {
  try {
    console.log('Checking if API server is running...');
    const response = await axios.get('http://localhost:3001/', {
      timeout: 5000
    });
    console.log('✅ API server is running!');
    return true;
  } catch (error) {
    console.log('❌ API server is not responding:', error.message);
    
    // Check if server-running.txt exists and read its content
    const serverRunningPath = path.join(__dirname, 'server-running.txt');
    if (fs.existsSync(serverRunningPath)) {
      const content = fs.readFileSync(serverRunningPath, 'utf8');
      console.log('Server status file exists:', content);
    } else {
      console.log('Server status file does not exist. Server might not be running.');
    }
    
    return false;
  }
}

// Function to test an endpoint with retries
async function testEndpoint(endpoint, retries = 2) {
  console.log(`\n--- Testing ${endpoint.name} API ---`);
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`Sending request to ${endpoint.url}... (Attempt ${attempt}/${retries})`);
      const response = await axios.post(endpoint.url, endpoint.data, {
        timeout: 10000 // 10 second timeout
      });
      console.log(`✅ ${endpoint.name} API responded successfully!`);
      console.log('Status:', response.status);
      console.log('Response data:', JSON.stringify(response.data, null, 2));
      return true;
    } catch (error) {
      console.log(`❌ ${endpoint.name} API request failed (Attempt ${attempt}/${retries})`);
      console.log('Error:', error.message);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('Connection refused. The server might not be running or is starting up.');
      } else if (error.code === 'ETIMEDOUT') {
        console.log('Connection timed out. The server might be busy or unresponsive.');
      }
      
      if (error.response) {
        console.log('Status:', error.response.status);
        console.log('Response data:', error.response.data);
      }
      
      if (attempt < retries) {
        const waitTime = 2000 * attempt; // Increase wait time with each retry
        console.log(`Waiting ${waitTime/1000} seconds before retrying...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      } else if (attempt === retries) {
        console.log('All retry attempts failed.');
        return false;
      }
    }
  }
  return false;
}

// Test all endpoints sequentially
async function runTests() {
  console.log('Starting API server tests...');
  
  // First check if the server is up
  const serverUp = await checkServerStatus();
  if (!serverUp) {
    console.log('Cannot proceed with tests as the API server is not responding.');
    console.log('Please make sure the server is running with: node stable-api-server.js');
    return;
  }
  
  // Wait a bit to ensure the server is fully initialized
  console.log('Waiting 2 seconds for server to fully initialize...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test each endpoint
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Test a second request to verify the server is still running
  console.log('\n--- Testing server persistence ---');
  console.log('Sending a second request to verify the server is still running...');
  await testEndpoint(endpoints[0]);
  
  // Final server status check
  await checkServerStatus();
  
  console.log('\nAll tests completed!');
}

// Run the tests
console.log('Waiting 3 seconds before starting tests to ensure server is ready...');
setTimeout(() => runTests(), 3000);
