import React, { useState, useEffect } from 'react';
import { supabase } from '../supabase/client';

const SupabaseConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('testing');
  const [results, setResults] = useState({});

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    const testResults = {};
    
    try {
      // Test 1: Basic connection
      console.log('Testing basic Supabase connection...');
      testResults.basicConnection = 'Testing...';
      setResults({...testResults});
      
      // Test 2: Auth status
      console.log('Testing auth status...');
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      testResults.authStatus = sessionError ? `Error: ${sessionError.message}` : (session ? 'Logged in' : 'Not logged in');
      setResults({...testResults});
      
      // Test 3: Database connection
      console.log('Testing database connection...');
      const { data: dbTest, error: dbError } = await supabase
        .from('questionnaire_responses')
        .select('count')
        .limit(1);
      testResults.databaseConnection = dbError ? `Error: ${dbError.message}` : 'Connected';
      setResults({...testResults});
      
      // Test 4: User profiles table
      console.log('Testing user_profiles table...');
      const { data: profileTest, error: profileError } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1);
      testResults.userProfilesTable = profileError ? `Error: ${profileError.message}` : 'Accessible';
      setResults({...testResults});
      
      testResults.basicConnection = 'Success';
      setConnectionStatus('completed');
      setResults({...testResults});
      
    } catch (error) {
      console.error('Connection test failed:', error);
      testResults.basicConnection = `Failed: ${error.message}`;
      setConnectionStatus('failed');
      setResults({...testResults});
    }
  };

  const forceReconnect = () => {
    setConnectionStatus('testing');
    setResults({});
    testConnection();
  };

  const goToLogin = () => {
    window.location.href = '/login';
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Supabase Connection Test</h2>
          <p className="text-gray-600 mt-2">Diagnosing connection issues...</p>
        </div>

        <div className="space-y-4">
          {/* Basic Connection */}
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span className="font-medium">Basic Connection:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              results.basicConnection === 'Success' ? 'bg-green-100 text-green-800' :
              results.basicConnection?.includes('Error') ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {results.basicConnection || 'Waiting...'}
            </span>
          </div>

          {/* Auth Status */}
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span className="font-medium">Auth Status:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              results.authStatus === 'Logged in' ? 'bg-green-100 text-green-800' :
              results.authStatus?.includes('Error') ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {results.authStatus || 'Waiting...'}
            </span>
          </div>

          {/* Database Connection */}
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span className="font-medium">Database Connection:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              results.databaseConnection === 'Connected' ? 'bg-green-100 text-green-800' :
              results.databaseConnection?.includes('Error') ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {results.databaseConnection || 'Waiting...'}
            </span>
          </div>

          {/* User Profiles Table */}
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span className="font-medium">User Profiles Table:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              results.userProfilesTable === 'Accessible' ? 'bg-green-100 text-green-800' :
              results.userProfilesTable?.includes('Error') ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {results.userProfilesTable || 'Waiting...'}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex space-x-3 justify-center">
          <button
            onClick={forceReconnect}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Again
          </button>
          <button
            onClick={goToLogin}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Go to Login
          </button>
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Continue Anyway
          </button>
        </div>

        {/* Status */}
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Status: <span className="font-medium">{connectionStatus}</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SupabaseConnectionTest;
