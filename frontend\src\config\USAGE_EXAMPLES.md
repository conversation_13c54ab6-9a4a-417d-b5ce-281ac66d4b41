# Model Configuration Usage Examples

This document provides practical examples of how to use the new configurable LLM model system.

## Quick Start

The system is now active! Here's what changed:

### For Users
- Model dropdowns in AI Agent and Strategy Generation now show only models available to your account
- No code changes needed - the system automatically applies your permissions

### For Administrators
- Access the Model Configuration admin panel via Profile → Model Configuration
- Configure which models are available to different clients
- Preview what models a client will see

## Common Use Cases

### 1. Setting Up a New Client

**Scenario**: You have a new client "<EMAIL>" who should only have access to basic models.

**Steps**:
1. Go to Profile → Model Configuration (admin only)
2. Click "Add Client"
3. Enter "<EMAIL>"
4. Uncheck expensive models like:
   - o1-preview
   - o1-mini
   - GPT-4o
   - Claude 3 Opus
5. Leave basic models enabled:
   - GPT-3.5 Turbo
   - Gemini 1.5 Flash
   - Claude 3 Haiku

**Result**: The client will only see the basic models in their dropdowns.

### 2. Creating Client Tiers

**Free Tier** (limited models):
```javascript
'<EMAIL>': {
  'o1-preview': false,
  'o1-mini': false,
  'gpt-4o': false,
  'gpt-4-turbo': false,
  'claude-3-opus-********': false,
  'claude-3-5-sonnet-********': false,
  'gemini-1.5-pro': false
}
```

**Premium Tier** (all models):
```javascript
'<EMAIL>': {
  // Empty = all models available
}
```

### 3. Temporarily Restricting Access

**Scenario**: A client has exceeded their usage quota and you want to temporarily limit them to free models.

**Quick Fix**:
1. Open Model Configuration admin
2. Find the client
3. Disable expensive models temporarily
4. Re-enable when quota resets

### 4. Demo/Trial Accounts

**Scenario**: You want to give prospects a limited demo experience.

**Configuration**:
```javascript
'<EMAIL>': {
  'o1-preview': false,
  'o1-mini': false,
  'gpt-4o': false,
  'gpt-4-turbo': false,
  'claude-3-opus-********': false,
  // Keep a few good models for demo
  'claude-3-5-sonnet-********': true,
  'gemini-1.5-pro': true,
  'deepseek-chat': true
}
```

## Testing Your Configuration

### Method 1: Preview Feature
1. Go to Model Configuration admin
2. Scroll to "Preview Client Access"
3. Enter any client email
4. See exactly what models they'll have access to

### Method 2: Test Account
1. Create a test user account
2. Configure their model access
3. Log in as that user
4. Check the model dropdowns in AI Agent or Strategy pages

## Code Examples

### Checking Model Availability Programmatically

```javascript
import { isModelAvailable, getAvailableModels } from '../config/modelConfig';

// Check if a specific model is available for a user
const canUseGPT4 = isModelAvailable('gpt-4o', null, user);

// Get all available models for a user
const userModels = getAvailableModels(null, user);
```

### Adding Custom Logic

You can extend the system by modifying `modelConfig.js`:

```javascript
// Example: Time-based access
export function getAvailableModels(clientEmail = null, user = null) {
  const baseModels = getBaseAvailableModels(clientEmail, user);
  
  // Custom logic: Disable expensive models during peak hours
  const currentHour = new Date().getHours();
  const isPeakHours = currentHour >= 9 && currentHour <= 17;
  
  if (isPeakHours && user?.tier === 'basic') {
    // Remove expensive models during business hours for basic users
    delete baseModels['🧠 Thinking Models'];
  }
  
  return baseModels;
}
```

## Troubleshooting

### Problem: Client sees wrong models
**Solution**: 
1. Check their email is correctly configured in `CLIENT_MODEL_CONFIGS`
2. Verify model IDs match exactly (case-sensitive)
3. Clear browser cache and reload

### Problem: Admin interface not accessible
**Solution**:
1. Ensure user email contains 'admin' or equals '<EMAIL>'
2. Modify the admin check in `ModelConfigAdmin.jsx` if needed

### Problem: Models not loading
**Solution**:
1. Check browser console for errors
2. Verify imports are correct in components
3. Ensure `getAvailableModels` is being called properly

## Best Practices

### 1. Start Conservative
- Begin with restricted access for new clients
- Gradually expand based on usage and payment tier

### 2. Document Changes
- Keep notes on why certain models are disabled
- Track client tier changes over time

### 3. Regular Reviews
- Monthly review of client configurations
- Remove unused client configurations
- Update model availability based on costs

### 4. Monitor Usage
- Track which models are being used most
- Identify opportunities to optimize costs
- Plan capacity based on popular models

### 5. Communication
- Inform clients about their available models
- Explain the benefits of different tiers
- Provide upgrade paths for more access

## Advanced Configuration

### Environment-Based Configuration
You can make configurations environment-specific:

```javascript
const isProduction = window.location.hostname !== 'localhost';

export const CLIENT_MODEL_CONFIGS = {
  // Development: More permissive
  ...(isProduction ? {} : {
    '<EMAIL>': {} // All models in development
  }),
  
  // Production: Strict controls
  ...(isProduction ? {
    '<EMAIL>': {
      'o1-preview': false,
      'gpt-4o': false
    }
  } : {})
};
```

### API-Based Configuration
For dynamic configuration, you could load from an API:

```javascript
// Future enhancement: Load from API
const loadClientConfigs = async () => {
  try {
    const response = await fetch('/api/model-configs');
    const configs = await response.json();
    return configs;
  } catch (error) {
    console.warn('Failed to load dynamic configs, using static');
    return CLIENT_MODEL_CONFIGS;
  }
};
```

## Migration Guide

If you're upgrading from the old hardcoded system:

1. **Backup**: Save your current model lists
2. **Update**: The new system is backward compatible
3. **Configure**: Add client-specific restrictions as needed
4. **Test**: Verify all clients see appropriate models
5. **Monitor**: Watch for any issues in the first week

The system defaults to showing all models, so existing users won't lose access unless you explicitly configure restrictions.
