import React, { useState, useEffect } from 'react';
import axios from 'axios';
import config from '../config';

/**
 * ResponseViewer component
 *
 * Displays a list of saved questionnaire responses and allows viewing individual responses.
 */
function ResponseViewer() {
  const [responses, setResponses] = useState([]);
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch all responses on component mount
  useEffect(() => {
    fetchResponses();
  }, []);

  // Fetch responses from the backend
  const fetchResponses = async () => {
    try {
      setLoading(true);
      const response = await axios.get(config.endpoints.responses.list);
      if (response.data && response.data.files) {
        setResponses(response.data.files);
      }
    } catch (err) {
      console.error('Error fetching responses:', err);
      setError('Failed to load responses from the server');
    } finally {
      setLoading(false);
    }
  };

  // Fetch a specific response by ID
  const fetchResponseDetails = async (fileId) => {
    try {
      setLoading(true);
      const response = await axios.get(config.endpoints.responses.get(fileId));
      setSelectedResponse(response.data);
    } catch (err) {
      console.error('Error fetching response details:', err);
      setError('Failed to load response details');
    } finally {
      setLoading(false);
    }
  };

  // Download a response as JSON
  const handleDownload = async (fileId) => {
    try {
      window.open(config.endpoints.responses.download(fileId), '_blank');
    } catch (err) {
      console.error('Error downloading response:', err);
      setError('Failed to download response');
    }
  };

  // Format timestamp for display
  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  };

  // Render response details
  const renderResponseDetails = () => {
    if (!selectedResponse) return null;

    return (
      <div className="mt-6 p-4 border rounded bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="raleway-title-h2">Response Details</h3>
          <button
            className="omega-nav-btn"
            onClick={() => setSelectedResponse(null)}
          >
            Close
          </button>
        </div>
        
        <div className="mb-4">
          <p className="body-text"><span className="bold-heavy">Questionnaire:</span> {selectedResponse.questionnaire}</p>
          <p className="body-text"><span className="bold-heavy">Timestamp:</span> {formatDate(selectedResponse.timestamp)}</p>
          <p className="body-text"><span className="bold-heavy">ID:</span> {selectedResponse.id}</p>
        </div>
        
        <div className="mt-4">
          <h4 className="raleway-title-h3 mb-2">Responses:</h4>
          <div className="bg-white p-3 rounded border shadow-sm">
            {Object.entries(selectedResponse.responses || {}).map(([key, value]) => (
              <div key={key} className="mb-2 pb-2 border-b last:border-b-0">
                <p className="raleway-menu">{key}:</p>
                <p className="pl-4 body-text">
                  {Array.isArray(value) 
                    ? value.join(', ') 
                    : String(value || 'No response')}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded shadow mt-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="raleway-title-h1">Questionnaire Responses</h2>
        <button
          className="omega-nav-btn"
          onClick={fetchResponses}
        >
          Refresh
        </button>
      </div>
      
      {loading && <div className="body-text">Loading...</div>}
      {error && <div className="text-red-600 mt-2 body-text">{error}</div>}
      
      {responses.length === 0 && !loading ? (
        <div className="body-text p-4 border rounded bg-white shadow-sm">
          No responses found. Submit a questionnaire to see responses here.
        </div>
      ) : (
        <div className="border rounded overflow-hidden shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-white">
              <tr>
                <th className="px-6 py-3 text-left raleway-menu text-blue-500 uppercase tracking-wider">
                  Filename
                </th>
                <th className="px-6 py-3 text-right raleway-menu text-blue-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {responses.map((file, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap body-text">
                    {file}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <button
                      className="omega-nav-btn mr-4"
                      onClick={() => fetchResponseDetails(file)}
                    >
                      View
                    </button>
                    <button
                      className="omega-nav-btn"
                      onClick={() => handleDownload(file)}
                    >
                      Download
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {renderResponseDetails()}
    </div>
  );
}

export default ResponseViewer;
