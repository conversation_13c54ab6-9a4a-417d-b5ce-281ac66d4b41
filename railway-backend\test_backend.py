#!/usr/bin/env python3
"""
Test script for the Railway backend API
"""

import asyncio
import httpx
import json

BASE_URL = "http://localhost:8000"

async def test_health():
    """Test the health endpoint"""
    print("Testing health endpoint...")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/health")
            print(f"Health check status: {response.status_code}")
            print(f"Response: {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False

async def test_llm_endpoint(endpoint, model, prompt="What is market research?"):
    """Test an LLM endpoint"""
    print(f"\nTesting {endpoint} endpoint with model {model}...")
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            payload = {
                "prompt": prompt,
                "model": model,
                "thinkingMode": False
            }
            
            response = await client.post(
                f"{BASE_URL}/api/{endpoint}",
                json=payload
            )
            
            print(f"Status: {response.status_code}")
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Model: {result.get('model', 'unknown')}")
            print(f"Response preview: {result.get('text', '')[:100]}...")
            
            if 'mock' in result:
                print("Note: This is a mock response (no API key configured)")
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"Test failed: {e}")
            return False

async def main():
    """Run all tests"""
    print("=== Railway Backend API Tests ===\n")
    
    # Test health endpoint
    health_ok = await test_health()
    
    if not health_ok:
        print("Health check failed. Make sure the server is running.")
        return
    
    # Test LLM endpoints
    tests = [
        ("gemini", "gemini-1.5-pro"),
        ("openai", "gpt-4o"),
        ("deepseek", "deepseek-chat"),
        ("anthropic", "claude-3-opus"),
        ("groq", "llama3-70b-8192")
    ]
    
    results = []
    for endpoint, model in tests:
        success = await test_llm_endpoint(endpoint, model)
        results.append((endpoint, success))
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Health check: {'✓' if health_ok else '✗'}")
    for endpoint, success in results:
        print(f"{endpoint}: {'✓' if success else '✗'}")
    
    all_passed = health_ok and all(success for _, success in results)
    print(f"\nOverall: {'All tests passed!' if all_passed else 'Some tests failed.'}")

if __name__ == "__main__":
    asyncio.run(main())
