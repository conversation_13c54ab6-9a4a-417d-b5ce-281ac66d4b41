// Express server to handle API requests
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Import API handlers
const geminiHandler = require('./api/gemini');
const openaiHandler = require('./api/openai');
// Use the test handler for DeepSeek to ensure it works
const deepseekHandler = require('./api/deepseek-test');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// API routes
app.post('/api/gemini', geminiHandler);
app.post('/api/openai', openaiHandler);

// Use the test handler for DeepSeek
app.post('/api/deepseek', (req, res) => {
  console.log('DeepSeek endpoint called');
  console.log('Request body:', req.body);
  return deepseekHandler(req, res);
});

// Serve static files from the frontend build directory in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'frontend/dist')));
  
  // Handle React routing, return all requests to React app
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'frontend/dist', 'index.html'));
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Gemini: http://localhost:${PORT}/api/gemini`);
  console.log(`- OpenAI: http://localhost:${PORT}/api/openai`);
  console.log(`- DeepSeek: http://localhost:${PORT}/api/deepseek`);
});
