import { supabase } from './client.js';

/**
 * Creates the company_profiles table in Supabase
 * This table will store company profile information linked to users
 */
async function createCompanyProfilesTable() {
  try {
    console.log('Creating company_profiles table in Supabase...');
    
    // Check if the table already exists
    const { data: existingTables, error: checkError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'company_profiles');
    
    if (checkError) {
      console.error('Error checking if table exists:', checkError);
      return false;
    }
    
    if (existingTables && existingTables.length > 0) {
      console.log('company_profiles table already exists');
      return true;
    }
    
    // Create the table using SQL
    const { error } = await supabase.rpc('create_company_profiles_table', {});
    
    if (error) {
      console.error('Error creating company_profiles table:', error);
      return false;
    }
    
    console.log('Successfully created company_profiles table!');
    return true;
  } catch (err) {
    console.error('Exception when creating company_profiles table:', err);
    return false;
  }
}

// Run the migration
createCompanyProfilesTable().then(success => {
  if (success) {
    console.log('✅ company_profiles table creation successful!');
  } else {
    console.error('❌ company_profiles table creation failed!');
  }
});

export default createCompanyProfilesTable;
