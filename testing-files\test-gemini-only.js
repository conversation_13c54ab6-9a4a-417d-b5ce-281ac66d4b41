// Simple test script for Gemini API
const dotenv = require('dotenv');
const path = require('path');
const { GoogleGenerativeAI } = require('@google/generative-ai');

// Load environment variables
console.log('Loading environment variables...');
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Normalize environment variables
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;

// Get API key
const apiKey = process.env.GEMINI_API_KEY;
console.log(`API Key available: ${apiKey ? 'Yes' : 'No'}`);
console.log(`API Key starts with: ${apiKey ? apiKey.substring(0, 5) + '...' : 'N/A'}`);

async function testGemini() {
  try {
    console.log('Initializing Gemini API...');
    const genAI = new GoogleGenerativeAI(apiKey);
    
    // Try with the model used in the application
    const modelName = 'gemini-1.5-pro';
    console.log(`Using model: ${modelName}`);
    
    const model = genAI.getGenerativeModel({ model: modelName });
    
    const prompt = 'Generate a short marketing tagline for an eco-friendly water bottle.';
    console.log(`Sending prompt: "${prompt}"`);
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('Success! Response:');
    console.log(text);
    
    return true;
  } catch (error) {
    console.error('Error testing Gemini API:');
    console.error(error.message);
    
    if (error.message.includes('not found')) {
      console.log('\nPossible issue with model name. The model might not be available or the name has changed.');
      console.log('Check the latest documentation at https://ai.google.dev/');
    }
    
    return false;
  }
}

// Run the test
testGemini().then(success => {
  console.log(`\nTest ${success ? 'PASSED' : 'FAILED'}`);
});
