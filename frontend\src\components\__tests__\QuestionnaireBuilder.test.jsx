import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import QuestionnaireBuilder from '../QuestionnaireBuilder';
import axios from 'axios';

// Mock axios
jest.mock('axios');

// Mock js-yaml
jest.mock('js-yaml', () => ({
  load: jest.fn().mockReturnValue({
    title: 'Test Questionnaire',
    description: 'Test description',
    sections: [
      {
        title: 'Test Section',
        questions: [
          {
            id: 'q1',
            text: 'Test Question 1',
            type: 'text',
            required: true
          },
          {
            id: 'q2',
            text: 'Test Question 2',
            type: 'radio',
            options: ['Option A', 'Option B', 'Option C'],
            required: false
          }
        ]
      }
    ]
  })
}));

describe('QuestionnaireBuilder Component', () => {
  beforeEach(() => {
    // Mock the axios get response for yaml list
    axios.get.mockResolvedValue({
      data: ['test-questionnaire.yaml', 'another-questionnaire.yaml']
    });
    
    // Mock the axios post response for saving responses
    axios.post.mockResolvedValue({
      data: {
        success: true,
        id: 'test-id-123',
        file: 'test_questionnaire_test-id-123.json'
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders the component with questionnaire dropdown', async () => {
    render(<QuestionnaireBuilder />);
    
    // Wait for the component to load the questionnaire list
    await waitFor(() => {
      expect(screen.getByText('Select a questionnaire:')).toBeInTheDocument();
    });
    
    // Check if the dropdown has options
    const dropdown = screen.getByLabelText('Select a questionnaire:');
    expect(dropdown).toBeInTheDocument();
  });

  test('loads and displays a questionnaire when selected', async () => {
    render(<QuestionnaireBuilder />);
    
    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Select a questionnaire:')).toBeInTheDocument();
    });
    
    // Select a questionnaire
    fireEvent.change(screen.getByLabelText('Select a questionnaire:'), {
      target: { value: 'test-questionnaire.yaml' }
    });
    
    // Wait for the questionnaire to load
    await waitFor(() => {
      expect(screen.getByText('Test Questionnaire')).toBeInTheDocument();
      expect(screen.getByText('Test description')).toBeInTheDocument();
      expect(screen.getByText('Test Question 1')).toBeInTheDocument();
      expect(screen.getByText('Test Question 2')).toBeInTheDocument();
    });
  });

  test('validates required fields', async () => {
    render(<QuestionnaireBuilder />);
    
    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Select a questionnaire:')).toBeInTheDocument();
    });
    
    // Select a questionnaire
    fireEvent.change(screen.getByLabelText('Select a questionnaire:'), {
      target: { value: 'test-questionnaire.yaml' }
    });
    
    // Wait for the questionnaire to load
    await waitFor(() => {
      expect(screen.getByText('Test Question 1')).toBeInTheDocument();
    });
    
    // Try to submit without filling required fields
    fireEvent.click(screen.getByText('Submit to Server'));
    
    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText('Please fill in all required fields')).toBeInTheDocument();
    });
  });

  test('submits the form successfully', async () => {
    render(<QuestionnaireBuilder />);
    
    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Select a questionnaire:')).toBeInTheDocument();
    });
    
    // Select a questionnaire
    fireEvent.change(screen.getByLabelText('Select a questionnaire:'), {
      target: { value: 'test-questionnaire.yaml' }
    });
    
    // Wait for the questionnaire to load
    await waitFor(() => {
      expect(screen.getByText('Test Question 1')).toBeInTheDocument();
    });
    
    // Fill in the required field
    fireEvent.change(screen.getByPlaceholderText(''), {
      target: { value: 'Test Answer' }
    });
    
    // Submit the form
    fireEvent.click(screen.getByText('Submit to Server'));
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText(/Responses submitted successfully/)).toBeInTheDocument();
    });
    
    // Verify that axios.post was called with the right data
    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:8000/api/responses/save',
      expect.objectContaining({
        questionnaire: 'Test Questionnaire',
        responses: expect.objectContaining({
          q1: 'Test Answer'
        })
      })
    );
  });
});
