import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DateInput, RatingInput, FileUploadInput, SliderInput } from '../AdvancedInputs';

describe('DateInput Component', () => {
  const mockOnChange = vi.fn();
  const props = {
    id: 'date1',
    value: '2025-05-31',
    required: true,
    onChange: mockOnChange
  };

  test('renders correctly with provided value', () => {
    render(<DateInput {...props} />);
    const dateInput = screen.getByRole('textbox');
    expect(dateInput).toBeInTheDocument();
    expect(dateInput).toHaveValue('2025-05-31');
    expect(dateInput).toBeRequired();
  });

  test('calls onChange when date is changed', () => {
    render(<DateInput {...props} />);
    const dateInput = screen.getByRole('textbox');
    fireEvent.change(dateInput, { target: { value: '2025-06-01' } });
    expect(mockOnChange).toHaveBeenCalledWith('date1', '2025-06-01', 'date');
  });
});

describe('RatingInput Component', () => {
  const mockOnChange = vi.fn();
  const props = {
    id: 'rating1',
    value: 3,
    required: true,
    onChange: mockOnChange,
    max: 5
  };

  test('renders stars correctly based on value', () => {
    render(<RatingInput {...props} />);
    const stars = screen.getAllByText('★');
    expect(stars).toHaveLength(5);
    
    // First 3 stars should be filled (yellow)
    for (let i = 0; i < 3; i++) {
      expect(stars[i]).toHaveClass('text-yellow-500');
    }
    
    // Last 2 stars should be empty (gray)
    for (let i = 3; i < 5; i++) {
      expect(stars[i]).toHaveClass('text-gray-300');
    }
  });

  test('calls onChange when a star is clicked', () => {
    render(<RatingInput {...props} />);
    const stars = screen.getAllByText('★');
    fireEvent.click(stars[4]); // Click the 5th star
    expect(mockOnChange).toHaveBeenCalledWith('rating1', 5, 'rating');
  });
});

describe('FileUploadInput Component', () => {
  const mockOnChange = vi.fn();
  const props = {
    id: 'file1',
    value: null,
    required: true,
    onChange: mockOnChange,
    accept: '.pdf,.jpg'
  };

  test('renders file input correctly', () => {
    render(<FileUploadInput {...props} />);
    const fileInput = screen.getByRole('textbox');
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute('accept', '.pdf,.jpg');
    expect(fileInput).toBeRequired();
  });

  test('displays selected file info when a file is selected', () => {
    const propsWithFile = {
      ...props,
      value: { name: 'test.pdf', size: 1024 * 10 } // 10KB file
    };
    
    render(<FileUploadInput {...propsWithFile} />);
    expect(screen.getByText('Selected file: test.pdf (10 KB)')).toBeInTheDocument();
  });
});

describe('SliderInput Component', () => {
  const mockOnChange = vi.fn();
  const props = {
    id: 'slider1',
    value: 50,
    required: true,
    onChange: mockOnChange,
    min: 0,
    max: 100,
    step: 1
  };

  test('renders slider with correct min, max, and value', () => {
    render(<SliderInput {...props} />);
    const slider = screen.getByRole('slider');
    expect(slider).toBeInTheDocument();
    expect(slider).toHaveAttribute('min', '0');
    expect(slider).toHaveAttribute('max', '100');
    expect(slider).toHaveAttribute('step', '1');
    expect(slider).toHaveValue('50');
  });

  test('displays current value', () => {
    render(<SliderInput {...props} />);
    expect(screen.getByText('50')).toBeInTheDocument();
  });

  test('calls onChange when slider value changes', () => {
    render(<SliderInput {...props} />);
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: '75' } });
    expect(mockOnChange).toHaveBeenCalledWith('slider1', 75, 'slider');
  });
});
