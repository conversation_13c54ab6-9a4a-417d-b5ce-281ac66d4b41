# LLM Model Configuration Guide

This guide explains how to configure which AI models are available to different clients in your market research tool.

## Overview

The system now supports configurable model access, allowing you to:
- Control which AI models are displayed in dropdown lists
- Set different model availability for different clients
- Easily manage client access without code changes
- Preview what models a client will see

## Files Involved

### 1. `modelConfig.js`
The main configuration file that defines:
- All available models with their properties
- Default model availability
- Client-specific configurations
- Utility functions for model access

### 2. `clientModelAccess.json`
A JSON configuration file for easier management of client access (optional, for future use).

### 3. `ModelConfigAdmin.jsx`
An admin interface component for managing model configurations through the UI.

## How It Works

### Default Behavior
By default, all models are available to all clients unless explicitly disabled.

### Client-Specific Configuration
You can restrict access for specific clients by adding their email to the `CLIENT_MODEL_CONFIGS` object in `modelConfig.js`.

## Configuration Examples

### Example 1: Basic Tier Client
```javascript
'<EMAIL>': {
  'o1-preview': false,        // Disable premium reasoning model
  'o1-mini': false,           // Disable reasoning model
  'gpt-4o': false,            // Disable latest GPT-4
  'gpt-4-turbo': false,       // Disable GPT-4 Turbo
  'claude-3-opus-20240229': false, // Disable premium Claude
  // All other models remain available
}
```

### Example 2: Budget Client
```javascript
'<EMAIL>': {
  'o1-preview': false,
  'o1-mini': false,
  'gpt-4o': false,
  'gpt-4-turbo': false,
  'claude-3-opus-20240229': false,
  'claude-3-5-sonnet-20241022': false,
  'gemini-1.5-pro': false,
  // Only free/low-cost models available
}
```

### Example 3: Premium Client
```javascript
'<EMAIL>': {
  // Empty object = all models available
}
```

## Adding New Clients

### Method 1: Edit Configuration File
1. Open `frontend/src/config/modelConfig.js`
2. Add a new entry to `CLIENT_MODEL_CONFIGS`:
```javascript
'<EMAIL>': {
  'model-to-disable': false,
  'another-model-to-disable': false,
  // Models not listed here will be available
}
```

### Method 2: Use Admin Interface
1. Navigate to the Model Configuration Admin page
2. Click "Add Client"
3. Enter the client's email address
4. Configure their model access using checkboxes

## Available Models

The system currently supports these models:

### 🧠 Thinking Models
- `o1-preview` - OpenAI o1-preview (Best Reasoning)
- `o1-mini` - OpenAI o1-mini (Fast Reasoning)
- `claude-3-5-sonnet-20241022` - Claude 3.5 Sonnet (Excellent Reasoning)
- `deepseek-r1` - DeepSeek R1 (Budget Reasoning)

### OpenAI Models
- `gpt-4o` - GPT-4o
- `gpt-4-turbo` - GPT-4 Turbo
- `gpt-3.5-turbo` - GPT-3.5 Turbo

### Google Gemini Models
- `gemini-1.5-pro` - Gemini 1.5 Pro
- `gemini-1.5-flash` - Gemini 1.5 Flash
- `gemini-pro` - Gemini Pro

### Anthropic Models
- `claude-3-opus-20240229` - Claude 3 Opus
- `claude-3-haiku-20240307` - Claude 3 Haiku

### DeepSeek Models
- `deepseek-chat` - DeepSeek Chat

### Groq Models
- `llama3-70b-8192` - Llama 3 70B
- `llama3-8b-8192` - Llama 3 8B
- `mixtral-8x7b-32768` - Mixtral 8x7B

## Adding New Models

To add a new model to the system:

1. Add the model definition to `ALL_MODELS` in `modelConfig.js`:
```javascript
'new-model-id': {
  value: 'new-model-id',
  label: 'New Model Name',
  provider: 'Provider Name',
  category: 'Model Category',
  requiresApiKey: false,
  cost: 'low|medium|high',
  description: 'Model description'
}
```

2. Add it to the `DEFAULT_MODEL_CONFIG`:
```javascript
'new-model-id': true, // Available by default
```

3. Update the backend to handle the new model if needed.

## Testing Configuration

### Preview Client Access
Use the admin interface to preview what models a specific client will see:
1. Go to the "Preview Client Access" section
2. Enter a client email
3. View their available models

### Test in Components
The model configuration is automatically used in:
- `AIAgent.jsx` - For general AI queries
- `StrategyPage.jsx` - For marketing strategy generation

## Best Practices

1. **Start Restrictive**: For new clients, start with limited access and expand as needed
2. **Document Changes**: Keep track of why certain models are disabled for clients
3. **Regular Review**: Periodically review client configurations to ensure they're still appropriate
4. **Cost Awareness**: Consider model costs when granting access to premium models
5. **Backup Configurations**: Export configurations regularly using the admin interface

## Troubleshooting

### Client Not Seeing Expected Models
1. Check if the client email is correctly configured in `CLIENT_MODEL_CONFIGS`
2. Verify the model IDs match exactly (case-sensitive)
3. Check browser console for any errors

### Admin Interface Not Accessible
1. Ensure the user has admin privileges (check `isAdmin` logic in `ModelConfigAdmin.jsx`)
2. Verify the user is logged in

### Models Not Loading
1. Check that the model configuration is properly imported in components
2. Verify the `getAvailableModels` function is being called correctly

## Future Enhancements

Potential improvements to consider:
- Database-backed configuration instead of file-based
- Role-based access control
- Usage tracking and analytics
- Automatic tier management based on subscription
- API for external configuration management

## Support

For questions or issues with model configuration, check:
1. Browser console for error messages
2. Component props and state in React DevTools
3. Network requests to ensure proper API communication
