# Prompt for Continuing Marketing Research Tool Development in Another AI IDE

## Stack
- **Frontend:** React (Vite, Tailwind CSS), YAML questionnaires in `public/`
- **Backend:** FastAPI (Python), endpoints for saving/listing questionnaires and responses
- **Testing:** Pytest (backend), React Testing Library (frontend)

## Current State
- Frontend loads YAML questionnaire files from `public/` (e.g., `spiritual-fine-jewelry-survey.yaml`), parses, and renders dynamic forms.
- Users can fill out the questionnaire and save responses as a JSON file.
- YAML file structure is validated (2-space indentation).
- Backend is set up but not yet integrated for saving responses from the frontend.

## What To Do Next
1. **Save Responses to Backend:**
   - Add a button to submit the filled questionnaire to FastAPI (POST `/api/responses/save`).
   - Backend saves each response as a JSON file in `/data/responses/`.
   - Add endpoint to list/download responses.
2. **List YAML Questionnaires Automatically:**
   - List all `.yaml` files in `public/` and populate the dropdown dynamically.
3. **Add More Input Types:**
   - Support for rating, date, file upload, etc.
4. **Add Validation and User Feedback:**
   - Show validation errors for required fields and success messages.
5. **Polish the UI:**
   - Add navigation, section explanations, and minimalist style.

## Instructions for the AI
- Use best practices for React and FastAPI.
- Keep code modular and well-documented.
- Follow PEP8 and use type hints for Python.
- Use Tailwind CSS for styling.
- Add comments and docstrings for clarity.
- Update README and TASK.md for new features.

## Sample YAML Questionnaire
```yaml
title: Spiritual Fine Jewelry Customer Profile Survey
description: >
  This survey helps us identify and deeply understand the characteristics of customers most likely to value spiritual fine jewelry. Your responses will help us create a tailored experience.
sections:
  - title: Section 1: Demographic Information
    questions:
      - id: age_range
        text: What is your age range?
        type: radio
        options:
          - Under 25
          - 25-34
          - 35-44
          - 45-54
          - 55-64
          - 65+
        required: true
      # ... more questions ...
```

**Continue development from here, starting with saving responses to the backend and listing them.** 