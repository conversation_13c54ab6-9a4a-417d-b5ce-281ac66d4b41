import React, { useState, useEffect } from 'react';
import { supabase } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

/**
 * ResponseDebug component
 * 
 * A comprehensive debugging tool to view all questionnaire responses
 * and diagnose issues with response viewing
 */
function ResponseDebug() {
  const [allResponses, setAllResponses] = useState([]);
  const [userResponses, setUserResponses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user } = useAuth();
  
  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      
      try {
        // Fetch all responses regardless of user
        const { data: allData, error: allError } = await supabase
          .from('questionnaire_responses')
          .select('*')
          .order('created_at', { ascending: false });
          
        if (allError) {
          console.error('Error fetching all responses:', allError);
          setError('Failed to fetch all responses: ' + allError.message);
        } else {
          console.log('All responses:', allData);
          setAllResponses(allData || []);
        }
        
        // Fetch user-specific responses if logged in
        if (user) {
          console.log('Fetching responses for user:', user.id);
          
          const { data: userData, error: userError } = await supabase
            .from('questionnaire_responses')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
            
          if (userError) {
            console.error('Error fetching user responses:', userError);
          } else {
            console.log('User responses:', userData);
            setUserResponses(userData || []);
          }
        }
      } catch (err) {
        console.error('Exception during fetch:', err);
        setError('An unexpected error occurred: ' + err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAllData();
  }, [user]);
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown Date';
    
    try {
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      }) + ' at ' + date.toLocaleTimeString('en-US');
    } catch (err) {
      return 'Date Error';
    }
  };
  
  // Test saving a response
  const testSaveResponse = async () => {
    if (!user) {
      setError('You must be logged in to test saving a response');
      return;
    }
    
    try {
      const testResponse = {
        questionnaire_type: 'consumerInsight',
        questionnaire_name: 'Consumer Insights',
        responses: { test_question: 'test_answer' },
        created_at: new Date().toISOString(),
        user_id: user.id
      };
      
      console.log('Saving test response:', testResponse);
      
      const { data, error } = await supabase
        .from('questionnaire_responses')
        .insert([testResponse])
        .select();
        
      if (error) {
        console.error('Error saving test response:', error);
        setError('Failed to save test response: ' + error.message);
      } else {
        console.log('Test response saved successfully:', data);
        alert('Test response saved successfully!');
        
        // Refresh the data
        window.location.reload();
      }
    } catch (err) {
      console.error('Exception during test save:', err);
      setError('An unexpected error occurred: ' + err.message);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6 text-blue-800">Response Debugging</h1>
      
      {/* User info */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4 text-blue-700">User Information</h2>
        {user ? (
          <div>
            <p><strong>User ID:</strong> {user.id}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Status:</strong> <span className="text-green-600">Authenticated</span></p>
          </div>
        ) : (
          <p className="text-red-600">Not authenticated. Please log in to view your responses.</p>
        )}
      </div>
      
      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-8">
          <h3 className="font-semibold">Error</h3>
          <p>{error}</p>
        </div>
      )}
      
      {/* Test button */}
      <div className="mb-8">
        <button 
          onClick={testSaveResponse}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          disabled={!user}
        >
          Test Save Response
        </button>
        <p className="text-sm text-gray-600 mt-2">
          This will save a test response with your user ID to verify the system is working.
        </p>
      </div>
      
      {/* Loading indicator */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Loading responses...</p>
        </div>
      )}
      
      {/* User responses */}
      {!loading && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-700">Your Responses ({userResponses.length})</h2>
          
          {userResponses.length === 0 ? (
            <p className="text-gray-600">No responses found for your user account.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white">
                <thead>
                  <tr className="bg-gray-100 text-gray-700">
                    <th className="py-2 px-4 text-left">ID</th>
                    <th className="py-2 px-4 text-left">Type</th>
                    <th className="py-2 px-4 text-left">Name</th>
                    <th className="py-2 px-4 text-left">Date</th>
                    <th className="py-2 px-4 text-left">User ID</th>
                  </tr>
                </thead>
                <tbody>
                  {userResponses.map(response => (
                    <tr key={response.id} className="border-t border-gray-200">
                      <td className="py-2 px-4">{response.id}</td>
                      <td className="py-2 px-4">{response.questionnaire_type}</td>
                      <td className="py-2 px-4">{response.questionnaire_name}</td>
                      <td className="py-2 px-4">{formatDate(response.created_at)}</td>
                      <td className="py-2 px-4">
                        {response.user_id === user?.id ? (
                          <span className="text-green-600">{response.user_id} (You)</span>
                        ) : (
                          response.user_id || 'None'
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
      
      {/* All responses */}
      {!loading && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-700">All Responses ({allResponses.length})</h2>
          
          {allResponses.length === 0 ? (
            <p className="text-gray-600">No responses found in the database.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white">
                <thead>
                  <tr className="bg-gray-100 text-gray-700">
                    <th className="py-2 px-4 text-left">ID</th>
                    <th className="py-2 px-4 text-left">Type</th>
                    <th className="py-2 px-4 text-left">Name</th>
                    <th className="py-2 px-4 text-left">Date</th>
                    <th className="py-2 px-4 text-left">User ID</th>
                    <th className="py-2 px-4 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {allResponses.map(response => (
                    <tr key={response.id} className="border-t border-gray-200">
                      <td className="py-2 px-4">{response.id}</td>
                      <td className="py-2 px-4">{response.questionnaire_type}</td>
                      <td className="py-2 px-4">{response.questionnaire_name}</td>
                      <td className="py-2 px-4">{formatDate(response.created_at)}</td>
                      <td className="py-2 px-4">
                        {response.user_id === user?.id ? (
                          <span className="text-green-600">{response.user_id} (You)</span>
                        ) : (
                          response.user_id || 'None'
                        )}
                      </td>
                      <td className="py-2 px-4">
                        <button 
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => {
                            // Store in sessionStorage for viewing
                            const responseData = {
                              questionnaire: response.questionnaire_name,
                              responses: response.responses,
                              timestamp: response.created_at,
                              rawData: response
                            };
                            sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
                            window.open('/responses', '_blank');
                          }}
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ResponseDebug;
