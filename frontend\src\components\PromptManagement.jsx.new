import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import config from '../config';
import ReactMarkdown from 'react-markdown';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

// Default prompts from the application
const defaultPrompts = {
  marketingStrategy: {
    name: "Marketing Strategy",
    prompt: `Generate a comprehensive marketing strategy based on the following questionnaire responses: [RESPONSES]. 

You are a world-class marketing strategist with expertise in developing effective marketing plans across various industries. Create a detailed marketing strategy that addresses the specific needs, challenges, and opportunities identified in the questionnaire responses.

Your strategy should include the following sections, each with detailed and actionable content:
1. Executive Summary - A concise overview of the entire strategy
2. Market Analysis - Industry trends, competitive landscape, and market opportunities
3. Target Audience - Detailed customer personas with demographics, psychographics, and behavioral patterns
4. Positioning Strategy - Unique value proposition and brand positioning
5. Marketing Channels - Specific channels to reach the target audience with budget allocation percentages
6. Messaging Framework - Key messages, tone, and communication style
7. Content Strategy - Types of content to create for each stage of the customer journey
8. Budget Allocation - Detailed breakdown of marketing spend across channels and initiatives
9. Timeline - Phased approach with key milestones
10. Success Metrics - KPIs and measurement framework

Format your response using markdown with clear headings, bullet points, and numbered lists where appropriate. Be specific, practical, and data-driven in your recommendations.`,
    description: "Used to generate the main marketing strategy"
  },
  implementationPlan: {
    name: "Implementation Plan",
    prompt: `Based on the following questionnaire responses: [RESPONSES],

You are an experienced marketing implementation specialist. Create a detailed, actionable implementation plan for executing the marketing strategy that addresses the specific needs and challenges identified in the questionnaire responses.

Your implementation plan should include:

1. Immediate Actions (Next 30 Days)
   - List specific tasks with owners, deadlines, and required resources
   - Include quick wins that can show immediate results

2. Short-term Actions (1-3 Months)
   - Key initiatives to build momentum
   - Required team structure and responsibilities
   - Technology and tools needed

3. Medium-term Actions (3-6 Months)
   - Scaling successful initiatives
   - Performance review processes
   - Optimization strategies

4. Long-term Actions (6-12 Months)
   - Strategic expansion opportunities
   - Advanced measurement and analytics implementation

For each phase, include:
- Specific tasks and subtasks
- Resource requirements (people, budget, tools)
- Dependencies between tasks
- Risk factors and mitigation strategies
- Success criteria for each milestone

Format your response using markdown with clear headings, numbered lists, and bullet points for readability. Be practical, specific, and focused on execution.`,
    description: "Used to generate the implementation plan section"
  },
  recommendations: {
    name: "Recommendations",
    prompt: `Based on the following questionnaire responses: [RESPONSES],

You are a strategic marketing advisor with expertise in optimizing marketing performance. Provide specific, actionable recommendations to improve marketing effectiveness based on the questionnaire responses.

Your recommendations should cover:

1. Key Messaging and Positioning
   - Specific messaging frameworks that will resonate with the target audience
   - Positioning statements that differentiate from competitors
   - Tone and voice guidelines

2. High-Impact Marketing Channels
   - Prioritized list of channels with expected ROI
   - Channel-specific optimization strategies
   - Cross-channel integration opportunities

3. Content Strategy Recommendations
   - Content types and formats for each stage of the buyer journey
   - Content distribution strategies
   - Content performance metrics

4. Budget Optimization
   - Reallocation recommendations based on performance data
   - Cost-saving opportunities
   - Performance benchmarks

For each recommendation, include:
- The specific problem or opportunity it addresses
- Expected impact (high, medium, low)
- Implementation difficulty (high, medium, low)
- Timeline for implementation

Format your response using markdown with clear headings and bullet points. Focus on practical advice that can be implemented immediately as well as long-term strategic recommendations.`,
    description: "Used to generate specific recommendations"
  },
  geminiPrompt: {
    name: "Gemini System Prompt",
    prompt: `You are an expert marketing strategist and consultant with deep expertise in developing effective marketing strategies across various industries.

- Provide detailed, actionable, and data-driven marketing advice
- Tailor your recommendations to the specific industry, target audience, and business goals
- Base recommendations on data and insights from the questionnaire responses
- Prioritize recommendations based on potential impact and feasibility
- Consider both short-term tactics and long-term strategic initiatives

Please provide a helpful response to the following question: [QUESTION]`,
    description: "System prompt used for Gemini API calls"
  },
  geminiThinkingPrompt: {
    name: "Gemini Thinking Prompt",
    prompt: `You are an expert marketing strategist and consultant with deep expertise in developing effective marketing strategies across various industries. Please think through this problem step by step:

1. THINKING: First, analyze the question and relevant information. Consider different angles, potential approaches, and important factors. Think through your reasoning carefully.

2. ANSWER: Then provide your final, well-structured answer based on your thinking. Include specific, actionable recommendations with clear rationales.

Question: [QUESTION]`,
    description: "System prompt with thinking mode for Gemini API calls"
  }
};

export default function PromptManagement() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [prompts, setPrompts] = useState(defaultPrompts);
  const [activePrompt, setActivePrompt] = useState('marketingStrategy');
  const [editedPrompt, setEditedPrompt] = useState('');
  const [testData, setTestData] = useState('{\n  "industry": "Technology",\n  "target_audience": "Small business owners",\n  "budget": "$10,000 per month",\n  "timeline": "6 months"\n}');
  const [testResponse, setTestResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  // Load custom prompts from localStorage
  useEffect(() => {
    const loadPrompts = () => {
      const savedPrompts = localStorage.getItem('customPrompts');
      if (savedPrompts) {
        try {
          const parsedPrompts = JSON.parse(savedPrompts);
          // Merge with default prompts to ensure we have all the latest defaults
          const mergedPrompts = { ...defaultPrompts };
          
          // Only override prompts that exist in the defaults
          Object.keys(parsedPrompts).forEach(key => {
            if (mergedPrompts[key]) {
              mergedPrompts[key] = parsedPrompts[key];
            }
          });
          
          setPrompts(mergedPrompts);
        } catch (error) {
          console.error('Error parsing saved prompts:', error);
        }
      }
    };
    
    loadPrompts();
  }, []);

  // Set edited prompt when active prompt changes
  useEffect(() => {
    if (prompts[activePrompt]) {
      setEditedPrompt(prompts[activePrompt].prompt);
    }
  }, [activePrompt, prompts]);

  const handlePromptSelect = (promptKey) => {
    setActivePrompt(promptKey);
  };

  const handlePromptChange = (e) => {
    setEditedPrompt(e.target.value);
  };

  const handleSavePrompt = () => {
    try {
      const updatedPrompts = {
        ...prompts,
        [activePrompt]: {
          ...prompts[activePrompt],
          prompt: editedPrompt
        }
      };
      
      setPrompts(updatedPrompts);
      localStorage.setItem('customPrompts', JSON.stringify(updatedPrompts));
      alert('Prompt saved successfully!');
    } catch (error) {
      console.error('Error saving prompt:', error);
      alert('Failed to save prompt. Please try again.');
    }
  };

  const handleResetPrompt = () => {
    if (window.confirm('Are you sure you want to reset this prompt to its default value?')) {
      if (defaultPrompts[activePrompt]) {
        const updatedPrompts = {
          ...prompts,
          [activePrompt]: {
            ...defaultPrompts[activePrompt]
          }
        };
        
        setPrompts(updatedPrompts);
        setEditedPrompt(defaultPrompts[activePrompt].prompt);
        localStorage.setItem('customPrompts', JSON.stringify(updatedPrompts));
        alert('Prompt reset to default.');
      }
    }
  };

  const handleTestDataChange = (e) => {
    setTestData(e.target.value);
  };

  const handleTestPrompt = async () => {
    setIsLoading(true);
    setTestResponse('');
    
    try {
      let parsedTestData;
      try {
        parsedTestData = JSON.parse(testData);
      } catch (error) {
        alert('Invalid JSON in test data. Please check your format.');
        setIsLoading(false);
        return;
      }
      
      let processedPrompt = editedPrompt
        .replace('[RESPONSES]', JSON.stringify(parsedTestData))
        .replace('[QUESTION]', JSON.stringify(parsedTestData));
      
      console.log('Sending prompt to API:', processedPrompt);
      
      const response = await axios.post(config.endpoints.gemini.ask, {
        prompt: processedPrompt, // Changed from 'question' to 'prompt'
        model: 'gemini-1.5-pro',
        thinkingMode: activePrompt.includes('Thinking')
      });
      
      console.log('API response:', response.data);
      setTestResponse(response.data.answer);
    } catch (error) {
      console.error('Error testing prompt:', error);
      setTestResponse(`Error: ${error.message || 'Failed to generate response'}`);
      
      if (error.message && error.message.includes('Network Error')) {
        // Provide a mock response if the API server is not running
        setTestResponse(`# Mock Response (API server not running)

This is a sample response that would be generated if the API server was running. In a real scenario, the LLM would process your prompt and generate a response based on the test data you provided.

## Sample Output

Based on the test data for ${prompts[activePrompt].name}, here's what a response might look like:

- Target audience analysis for ${JSON.parse(testData)?.target_audience || 'your audience'}
- Marketing strategy for ${JSON.parse(testData)?.industry || 'your industry'}
- Budget allocation of ${JSON.parse(testData)?.budget || 'your budget'}
- Timeline considerations for ${JSON.parse(testData)?.timeline || 'your timeline'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Prompt Management</h1>
      <p className="mb-6">
        View and edit the prompts used to generate marketing strategies and other content in the application.
        Changes you make here will be saved to your browser and used for future generations.
      </p>
      
      {/* Horizontal Prompt Templates Menu */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Prompt Templates</h2>
        <div className="flex flex-wrap gap-3">
          {Object.keys(prompts).map((promptKey) => (
            <button 
              key={promptKey}
              className={`px-4 py-2 rounded-lg font-medium transition ${activePrompt === promptKey ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-200'}`}
              onClick={() => handlePromptSelect(promptKey)}
            >
              {prompts[promptKey].name}
            </button>
          ))}
        </div>
      </div>
      
      {/* Main Content Area - Full Width */}
      <div className="w-full">
        {prompts[activePrompt] && (
          <>
            <div className="bg-white p-6 rounded-lg shadow mb-6">
              <h2 className="text-2xl font-semibold mb-2">{prompts[activePrompt].name}</h2>
              <p className="text-gray-600 mb-4">{prompts[activePrompt].description}</p>
              
              <div className="mb-4">
                <label className="block text-gray-700 font-semibold mb-2">
                  Edit Prompt:
                </label>
                <textarea
                  className="w-full h-64 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={editedPrompt}
                  onChange={handlePromptChange}
                />
              </div>
              
              <div className="flex space-x-4">
                <button
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onClick={handleSavePrompt}
                >
                  Save Changes
                </button>
                <button
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  onClick={handleResetPrompt}
                >
                  Reset to Default
                </button>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-xl font-semibold mb-4">Test Prompt</h3>
              
              <Tabs selectedIndex={activeTab} onSelect={index => setActiveTab(index)}>
                <TabList className="mb-4">
                  <Tab>Test Data</Tab>
                  <Tab>Response</Tab>
                </TabList>
                
                <TabPanel>
                  <div className="mb-4">
                    <label className="block text-gray-700 font-semibold mb-2">
                      Sample Data (JSON):
                    </label>
                    <textarea
                      className="w-full h-64 p-3 border border-gray-300 rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={testData}
                      onChange={handleTestDataChange}
                    />
                    <p className="text-sm text-gray-500 mt-2">
                      This data will replace the [RESPONSES] or [QUESTION] placeholder in your prompt.
                    </p>
                  </div>
                  
                  <button
                    className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-400"
                    onClick={handleTestPrompt}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Testing...' : 'Test Prompt'}
                  </button>
                </TabPanel>
                
                <TabPanel>
                  <div className="border border-gray-300 rounded-lg p-4 min-h-64 bg-gray-50">
                    {testResponse ? (
                      <div className="prose max-w-none">
                        <ReactMarkdown>{testResponse}</ReactMarkdown>
                      </div>
                    ) : (
                      <p className="text-gray-500 italic">
                        {isLoading ? 'Generating response...' : 'Test a prompt to see the response here'}
                      </p>
                    )}
                  </div>
                </TabPanel>
              </Tabs>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
