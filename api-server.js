// Express server to handle API requests for all models
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables with explicit path
const result = dotenv.config({ path: path.resolve(__dirname, '.env') });

if (result.error) {
  console.error('Error loading .env file:', result.error);
} else {
  console.log('.env file loaded successfully');
}

// Normalize environment variables to handle different formats
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;
process.env.google_api_key = process.env.GEMINI_API_KEY || process.env.google_api_key;
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.openai_api_key;
process.env.openai_api_key = process.env.OPENAI_API_KEY || process.env.openai_api_key;
process.env.DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;
process.env.deepseek_api_key = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;

// Log environment variables to verify they're loaded
console.log('Environment variables loaded:');
console.log('- GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Available' : 'Not available');
console.log('- OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Available' : 'Not available');
console.log('- DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? 'Available' : 'Not available');

// Import API handlers
const geminiHandler = require('./api/gemini');
const openaiHandler = require('./api/openai');

// Create Express app
const app = express();
const PORT = 3000;

// Middleware
app.use(cors());

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', JSON.stringify(req.headers));
  next();
});

// Custom JSON body parser with safe error handling
app.use((req, res, next) => {
  express.json()(req, res, (err) => {
    if (err) {
      console.error('Invalid JSON in request body:', err.message);
      return res.status(400).json({ error: 'Invalid JSON in request body' });
    }
    next();
  });
});

app.use(express.urlencoded({ extended: true }));

// Test endpoint to verify server is running
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Market Research API Server</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <h1>Market Research API Server</h1>
        <p>The API server is running successfully on port ${PORT}</p>
        
        <div class="endpoint">
          <h2>Available Endpoints:</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Environment Status:</h2>
          <ul>
            <li><strong>Gemini API Key:</strong> <span class="${process.env.GEMINI_API_KEY ? 'success' : 'error'}">${process.env.GEMINI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>OpenAI API Key:</strong> <span class="${process.env.OPENAI_API_KEY ? 'success' : 'error'}">${process.env.OPENAI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>DeepSeek API Key:</strong> <span class="${process.env.deepseek_api_key ? 'success' : 'error'}">${process.env.deepseek_api_key ? 'Available' : 'Not Available'}</span></li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Test API Endpoints:</h2>
          <p>Use the following curl commands to test the API endpoints:</p>
          <pre>
# Test Gemini API
curl -X POST http://localhost:${PORT}/api/gemini -H "Content-Type: application/json" -d '{"prompt":"Hello, this is a test", "model":"gemini-pro"}'

# Test OpenAI API
curl -X POST http://localhost:${PORT}/api/openai -H "Content-Type: application/json" -d '{"prompt":"Hello, this is a test", "model":"gpt-3.5-turbo"}'

# Test DeepSeek API
curl -X POST http://localhost:${PORT}/api/deepseek -H "Content-Type: application/json" -d '{"prompt":"Hello, this is a test", "model":"deepseek-chat"}'
          </pre>
        </div>
      </body>
    </html>
  `);
});

// API routes for Gemini and OpenAI
app.post('/api/gemini', (req, res) => {
  console.log('Gemini API endpoint called');
  console.log('Request body:', req.body);
  return geminiHandler(req, res);
});

app.post('/api/openai', (req, res) => {
  console.log('OpenAI API endpoint called');
  console.log('Request body:', req.body);
  return openaiHandler(req, res);
});

// Simple DeepSeek API endpoint that always returns a successful response
app.post('/api/deepseek', (req, res) => {
  console.log('DeepSeek API endpoint called');
  console.log('Request body:', req.body);
  
  try {
    // Extract data from request
    const { prompt, model = 'deepseek-chat' } = req.body || {};
    
    console.log('Processing prompt:', prompt);
    console.log('Selected model:', model);
    
    // Check if we have a DeepSeek API key in environment variables
    // Normalize environment variables to handle different formats
    process.env.DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;
    process.env.deepseek_api_key = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;
    
    const apiKey = process.env.DEEPSEEK_API_KEY;
    
    if (!apiKey) {
      console.log('No DeepSeek API key found, using mock response');
      // In a production environment, you might want to return an error instead
      // return res.status(401).json({
      //   error: 'DeepSeek API key not configured',
      //   details: 'Please set the DEEPSEEK_API_KEY environment variable in your .env file.'
      // });
    } else {
      console.log('DeepSeek API key found, but using mock response for testing');
      // In the future, you could implement actual DeepSeek API calls here
    }
    
    // Return a mock response for testing
    return res.status(200).json({
      text: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      response: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      model: model
    });
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Global error handler - must be added after all routes
app.use((err, req, res, next) => {
  console.error('Unhandled error in request:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'The server encountered an unexpected error. Please try again later.',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Process-level error handling to prevent crashes
process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION! 💥');
  console.error(err.name, err.message, err.stack);
  // Log the error but keep the server running
  // In production, you might want to restart the server here
});

process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION! 💥');
  console.error(err.name, err.message, err.stack);
  // Log the error but keep the server running
});

// Keep the Node.js process alive even if there are errors
setInterval(() => {
  console.log('Server heartbeat check - still running at ' + new Date().toISOString());
}, 60000); // Log every minute to show the server is still alive

// Start server
const server = app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Gemini: http://localhost:${PORT}/api/gemini`);
  console.log(`- OpenAI: http://localhost:${PORT}/api/openai`);
  console.log(`- DeepSeek: http://localhost:${PORT}/api/deepseek`);
  console.log(`- Status page: http://localhost:${PORT}/`);
});

// Handle server shutdown gracefully
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
