#!/usr/bin/env python3
"""
Local development server starter for Railway backend
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if requirements are installed"""
    try:
        import fastapi
        import uvicorn
        import httpx
        import pydantic
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing package: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists and suggest setup"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        print("⚠ No .env file found")
        if env_example.exists():
            print("📝 Copy .env.example to .env and add your API keys:")
            print("   cp .env.example .env")
        else:
            print("📝 Create a .env file with your API keys:")
            print("   OPENAI_API_KEY=your_key_here")
            print("   GEMINI_API_KEY=your_key_here")
        print("   (The server will work with mock responses if no keys are provided)")
        return False
    else:
        print("✓ .env file found")
        return True

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting Railway backend server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🏥 Health check: http://localhost:8000/health")
    print("📚 API docs: http://localhost:8000/docs")
    print("\n⏹ Press Ctrl+C to stop the server\n")
    
    try:
        # Start uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")

def main():
    """Main function"""
    print("=== Railway Backend Local Development ===\n")
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("✗ main.py not found. Please run this script from the railway-backend directory.")
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment setup
    check_env_file()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
