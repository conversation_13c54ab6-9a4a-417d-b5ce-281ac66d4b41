// Simple Express server just for DeepSeek API
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 3000;

// Enable CORS and JSON parsing
app.use(cors());
app.use(express.json());

// Simple DeepSeek API endpoint
app.post('/api/deepseek', (req, res) => {
  console.log('DeepSeek API endpoint called');
  console.log('Request body:', req.body);
  
  try {
    const { prompt, model = 'deepseek-chat' } = req.body || {};
    
    console.log('Processing prompt:', prompt);
    console.log('Selected model:', model);
    
    // Return a mock response for testing
    return res.status(200).json({
      text: `This is a test response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      response: `This is a test response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      model: model
    });
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Test endpoint to verify server is running
app.get('/api/test', (req, res) => {
  return res.status(200).json({ message: 'Server is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Simple DeepSeek server running on port ${PORT}`);
  console.log(`DeepSeek API endpoint: http://localhost:${PORT}/api/deepseek`);
  console.log(`Test endpoint: http://localhost:${PORT}/api/test`);
});
