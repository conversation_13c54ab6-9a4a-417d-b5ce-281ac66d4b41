-- <PERSON><PERSON> function to create the company_profiles table
CREATE OR REPLACE FUNCTION create_company_profiles_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create the company_profiles table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.company_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT,
    industry TEXT,
    company_size TEXT,
    founded_year INTEGER,
    location TEXT,
    mission TEXT,
    vision TEXT,
    core_values TEXT,
    target_audience TEXT,
    unique_selling_points TEXT,
    competitors TEXT,
    challenges TEXT,
    business_goals TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id)
  );

  -- Add comment to the table
  COMMENT ON TABLE public.company_profiles IS 'Stores company profile information for users';

  -- Create RLS policies
  -- Enable RLS
  ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;

  -- Create policy for users to select their own company profile
  CREATE POLICY select_own_company_profile ON public.company_profiles
    FOR SELECT USING (auth.uid() = user_id);

  -- Create policy for users to insert their own company profile
  CREATE POLICY insert_own_company_profile ON public.company_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

  -- Create policy for users to update their own company profile
  CREATE POLICY update_own_company_profile ON public.company_profiles
    FOR UPDATE USING (auth.uid() = user_id);

  -- Create policy for users to delete their own company profile
  CREATE POLICY delete_own_company_profile ON public.company_profiles
    FOR DELETE USING (auth.uid() = user_id);

  -- Create trigger to update the updated_at column
  CREATE OR REPLACE FUNCTION update_company_profiles_updated_at()
  RETURNS TRIGGER AS $$
  BEGIN
    NEW.updated_at = now();
    RETURN NEW;
  END;
  $$ LANGUAGE plpgsql;

  CREATE TRIGGER update_company_profiles_updated_at
  BEFORE UPDATE ON public.company_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_company_profiles_updated_at();

END;
$$;
