// Simple DeepSeek API integration handler
const axios = require('axios');

// Export a simple handler function that returns a mock response for testing
module.exports = async (req, res) => {
  console.log('DeepSeek API handler called');
  console.log('Request body:', req.body);

  // Remove CSP headers completely
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('Content-Security-Policy-Report-Only');

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle OPTIONS requests
  if (req.method === 'OPTIONS') {
    res.status(204).end();
    return;
  }

  try {
    // Extract data from request
    const { prompt, model = 'deepseek-chat' } = req.body || {};
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }
    
    console.log('Processing prompt:', prompt);
    console.log('Selected model:', model);
    
    // Check if we have a DeepSeek API key in environment variables
    const apiKey = process.env.deepseek_api_key;
    
    if (apiKey) {
      console.log('DeepSeek API key found, attempting to call DeepSeek API');
      
      try {
        // Call the DeepSeek API
        const response = await axios.post(
          'https://api.deepseek.com/v1/chat/completions',
          {
            model: 'deepseek-chat',
            messages: [
              { role: 'system', content: 'You are a helpful marketing and client acquisition expert.' },
              { role: 'user', content: prompt }
            ],
            temperature: 0.7,
            max_tokens: 1000
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`
            }
          }
        );
        
        const responseText = response.data.choices[0].message.content;
        
        return res.status(200).json({
          text: responseText,
          response: responseText,
          model: model
        });
      } catch (apiError) {
        console.error('Error calling DeepSeek API:', apiError.message);
        
        // Return a fallback response for testing
        return res.status(200).json({
          text: `This is a fallback response from the DeepSeek API handler. The actual API call failed with error: ${apiError.message}. Your prompt was: ${prompt}`,
          response: `This is a fallback response from the DeepSeek API handler. The actual API call failed with error: ${apiError.message}. Your prompt was: ${prompt}`,
          model: model,
          error: apiError.message
        });
      }
    } else {
      console.log('No DeepSeek API key found, returning mock response');
      
      // Return a mock response for testing without an API key
      return res.status(200).json({
        text: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt}`,
        response: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt}`,
        model: model
      });
    }
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
};
