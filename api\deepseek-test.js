// Simple DeepSeek API test handler
const axios = require('axios');

// Export a simple handler function that always returns a successful response
module.exports = async (req, res) => {
  console.log('DeepSeek TEST API handler called');
  console.log('Request body:', req.body);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    res.status(204).end();
    return;
  }
  
  try {
    // Extract data from request
    const { prompt, model = 'deepseek-chat' } = req.body || {};
    
    console.log('Processing prompt:', prompt);
    console.log('Selected model:', model);
    
    // Always return a successful mock response
    return res.status(200).json({
      text: `This is a test response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      response: `This is a test response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      model: model
    });
  } catch (error) {
    console.error('Error in DeepSeek TEST handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
};
