const axios = require('axios');

async function testDeepSeekEndpoint() {
  try {
    console.log('Testing DeepSeek API endpoint...');
    
    const response = await axios.post(
      'http://localhost:3000/api/deepseek',
      {
        prompt: 'Test prompt for DeepSeek API',
        model: 'deepseek-chat'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Error testing DeepSeek API endpoint:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testDeepSeekEndpoint();
