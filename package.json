{"name": "market-research-tool", "version": "1.0.0", "description": "A tool for creating and analyzing market research questionnaires", "private": true, "scripts": {"dev": "cd frontend && npm run dev", "api": "node server.js", "dev:full": "concurrently \"npm run api\" \"npm run dev\"", "build": "cd frontend && npm install && npm run build && cd .. && node -e \"const fs=require('fs');const path=require('path');if(fs.existsSync('dist')){fs.rmSync('dist',{recursive:true,force:true});}fs.mkdirSync('dist',{recursive:true});const copyDir=(src,dest)=>{const entries=fs.readdirSync(src,{withFileTypes:true});for(const entry of entries){const srcPath=path.join(src,entry.name);const destPath=path.join(dest,entry.name);if(entry.isDirectory()){fs.mkdirSync(destPath,{recursive:true});copyDir(srcPath,destPath);}else{fs.copyFileSync(srcPath,destPath);}}};copyDir('frontend/dist','dist');\"", "start": "node simple-server.js", "start:main": "node stable-api-server.js", "start:health": "node healthcheck-server.js", "railway:build": "npm install && npm run build", "preview": "cd frontend && npm run preview", "test": "cd frontend && npm run test", "test-api": "node test-api.js"}, "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "js-yaml": "^4.1.0", "mkdirp": "^3.0.1", "openai": "^5.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "react-markdown": "^10.1.0", "react-tabs": "^6.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.14", "concurrently": "^9.1.2", "jsdom": "^22.1.0", "postcss": "^8.4.25", "tailwindcss": "^3.3.2", "vite": "^4.4.0", "vitest": "^0.33.0"}, "engines": {"node": ">=14.0.0"}}