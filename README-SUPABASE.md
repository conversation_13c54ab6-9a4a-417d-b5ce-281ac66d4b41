# Market Research Tool - Supabase Integration

This document explains how to set up Supabase for persistent storage of questionnaire responses in the Market Research Tool.

## Why Supabase?

We've implemented Supabase as a persistent storage solution for questionnaire responses because:

1. **True persistence** - Data is stored in the cloud and accessible from any device
2. **Structured data storage** - Proper database with querying capabilities
3. **User authentication** (future implementation) - Can be tied to user accounts
4. **Real-time capabilities** - For potential collaborative features

## Setup Instructions

### 1. Create a Supabase Account and Project

1. Go to [Supabase](https://supabase.com/) and sign up for an account
2. Create a new project
3. Note your project URL and anon key (public API key)

### 2. Create the Database Table

Run the following SQL in the Supabase SQL Editor:

```sql
CREATE TABLE questionnaire_responses (
  id SERIAL PRIMARY KEY,
  questionnaire_type TEXT NOT NULL,
  questionnaire_name TEXT NOT NULL,
  responses JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_questionnaire_type ON questionnaire_responses(questionnaire_type);
CREATE INDEX idx_created_at ON questionnaire_responses(created_at);
```

### 3. Configure Environment Variables

1. Create a `.env` file in the `frontend` directory
2. Add your Supabase credentials:

```
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 4. Run the Application

```bash
cd frontend
npm install
npm run dev
```

## How It Works

The application now uses Supabase for persistent storage with localStorage as a fallback:

1. **Saving Responses**: When a questionnaire is submitted, responses are saved to Supabase
2. **Loading Responses**: When viewing responses, the app first tries to fetch from Supabase, then falls back to localStorage if needed
3. **Tracking Completion**: The app tracks which questionnaires have been completed by checking Supabase for saved responses

## Fallback Mechanism

If Supabase is unavailable or there's an error:

1. The app will fall back to localStorage for saving and retrieving responses
2. This ensures the app remains functional even without an internet connection
3. When connectivity is restored, new submissions will go to Supabase

## Future Enhancements

Potential future enhancements with Supabase:

1. User authentication and personalized questionnaires
2. Real-time collaboration on questionnaires
3. Analytics on questionnaire responses
4. Multiple response versions with history tracking
