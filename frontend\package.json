{"name": "marketing-research-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build && node -e \"const fs=require('fs');const path=require('path');function copyRecursive(src,dest){if(!fs.existsSync(dest)){fs.mkdirSync(dest,{recursive:true})};const entries=fs.readdirSync(src,{withFileTypes:true});for(const entry of entries){const srcPath=path.join(src,entry.name);const destPath=path.join(dest,entry.name);if(entry.isDirectory()){copyRecursive(srcPath,destPath);}else{fs.copyFileSync(srcPath,destPath);}}};copyRecursive('public','dist/public');\"", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.49.9", "axios": "^1.9.0", "dompurify": "^3.2.6", "js-yaml": "^4.1.0", "marked": "^2.1.3", "openai": "^5.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.1", "react-tabs": "^6.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.0.0", "jsdom": "^23.0.1", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "vite": "^4.0.0", "vitest": "^1.0.4"}}