from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import os
import json
import httpx
import asyncio
from datetime import datetime
import uvicorn

# Import LLM service modules
from services.gemini_service import GeminiService
from services.openai_service import OpenAIService
from services.deepseek_service import DeepSeekService
from services.anthropic_service import AnthropicService
from services.groq_service import GroqService

app = FastAPI(
    title="Market Research Tool - LLM Backend",
    description="FastAPI backend for LLM operations",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your Vercel domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class LLMRequest(BaseModel):
    prompt: Optional[str] = None
    question: Optional[str] = None
    model: Optional[str] = None
    thinkingMode: Optional[bool] = False

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    service: str

# Initialize LLM services
gemini_service = GeminiService()
openai_service = OpenAIService()
deepseek_service = DeepSeekService()
anthropic_service = AnthropicService()
groq_service = GroqService()

# Health check endpoint for Railway
@app.get("/health", response_model=HealthResponse)
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for Railway deployment"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow().isoformat(),
        service="market-research-llm-backend"
    )

# Root endpoint
@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root API endpoint"""
    return {
        "message": "Market Research Tool - LLM Backend API",
        "version": "1.0.0",
        "endpoints": [
            "/api/gemini",
            "/api/openai", 
            "/api/deepseek",
            "/api/anthropic",
            "/api/groq",
            "/health"
        ],
        "status": "running"
    }

# Gemini API endpoint
@app.post("/api/gemini", response_model=Dict[str, Any])
async def gemini_api(request: LLMRequest):
    """Handle Gemini API requests"""
    try:
        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")
        
        model = request.model or "gemini-1.5-pro"
        thinking_mode = request.thinkingMode or False
        
        response = await gemini_service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# OpenAI API endpoint
@app.post("/api/openai", response_model=Dict[str, Any])
async def openai_api(request: LLMRequest):
    """Handle OpenAI API requests"""
    try:
        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")
        
        model = request.model or "gpt-4o"
        thinking_mode = request.thinkingMode or False
        
        response = await openai_service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# DeepSeek API endpoint
@app.post("/api/deepseek", response_model=Dict[str, Any])
async def deepseek_api(request: LLMRequest):
    """Handle DeepSeek API requests"""
    try:
        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")
        
        model = request.model or "deepseek-chat"
        thinking_mode = request.thinkingMode or False
        
        response = await deepseek_service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Anthropic API endpoint
@app.post("/api/anthropic", response_model=Dict[str, Any])
async def anthropic_api(request: LLMRequest):
    """Handle Anthropic API requests"""
    try:
        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")
        
        model = request.model or "claude-3-opus"
        thinking_mode = request.thinkingMode or False
        
        response = await anthropic_service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Groq API endpoint
@app.post("/api/groq", response_model=Dict[str, Any])
async def groq_api(request: LLMRequest):
    """Handle Groq API requests"""
    try:
        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")
        
        model = request.model or "llama3-70b-8192"
        thinking_mode = request.thinkingMode or False
        
        response = await groq_service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={"error": "Endpoint not found", "detail": "The requested endpoint does not exist"}
    )

@app.exception_handler(500)
async def server_error_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc.detail) if hasattr(exc, 'detail') else "An unexpected error occurred"
        }
    )

# Run the server when executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=False)
