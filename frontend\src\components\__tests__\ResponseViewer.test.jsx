import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ResponseViewer from '../ResponseViewer';
import axios from 'axios';

// Mock axios
jest.mock('axios');

describe('ResponseViewer Component', () => {
  const mockResponses = [
    {
      id: 'resp1',
      questionnaire: 'Test Questionnaire 1',
      timestamp: '2025-05-30T10:00:00Z',
      filename: 'test_questionnaire_1_resp1.json'
    },
    {
      id: 'resp2',
      questionnaire: 'Test Questionnaire 2',
      timestamp: '2025-05-31T11:00:00Z',
      filename: 'test_questionnaire_2_resp2.json'
    }
  ];

  const mockResponseDetail = {
    id: 'resp1',
    questionnaire: 'Test Questionnaire 1',
    timestamp: '2025-05-30T10:00:00Z',
    responses: {
      q1: 'Answer to question 1',
      q2: ['Option A', 'Option C'],
      q3: 5
    }
  };

  beforeEach(() => {
    // Mock the axios get response for listing responses
    axios.get.mockImplementation((url) => {
      if (url === 'http://localhost:8000/api/responses/list') {
        return Promise.resolve({ data: mockResponses });
      } else if (url === 'http://localhost:8000/api/responses/resp1') {
        return Promise.resolve({ data: mockResponseDetail });
      }
      return Promise.reject(new Error('Not found'));
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders the component with loading state initially', () => {
    render(<ResponseViewer />);
    expect(screen.getByText('Loading responses...')).toBeInTheDocument();
  });

  test('displays the list of responses after loading', async () => {
    render(<ResponseViewer />);
    
    // Wait for the responses to load
    await waitFor(() => {
      expect(screen.getByText('Saved Responses')).toBeInTheDocument();
    });
    
    // Check if the responses are displayed
    expect(screen.getByText('Test Questionnaire 1')).toBeInTheDocument();
    expect(screen.getByText('Test Questionnaire 2')).toBeInTheDocument();
    
    // Check if the timestamps are formatted correctly
    expect(screen.getByText(/May 30, 2025/)).toBeInTheDocument();
    expect(screen.getByText(/May 31, 2025/)).toBeInTheDocument();
  });

  test('shows response details when view button is clicked', async () => {
    render(<ResponseViewer />);
    
    // Wait for the responses to load
    await waitFor(() => {
      expect(screen.getByText('Test Questionnaire 1')).toBeInTheDocument();
    });
    
    // Click the view button for the first response
    const viewButtons = screen.getAllByText('View');
    fireEvent.click(viewButtons[0]);
    
    // Wait for the response details to load
    await waitFor(() => {
      expect(screen.getByText('Response Details')).toBeInTheDocument();
    });
    
    // Check if the response details are displayed
    expect(screen.getByText('Answer to question 1')).toBeInTheDocument();
    expect(screen.getByText('Option A, Option C')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  test('handles error when loading responses fails', async () => {
    // Mock axios to reject the request
    axios.get.mockRejectedValueOnce(new Error('Failed to fetch'));
    
    render(<ResponseViewer />);
    
    // Wait for the error message to appear
    await waitFor(() => {
      expect(screen.getByText(/Error loading responses/)).toBeInTheDocument();
    });
  });

  test('handles empty response list', async () => {
    // Mock axios to return an empty array
    axios.get.mockResolvedValueOnce({ data: [] });
    
    render(<ResponseViewer />);
    
    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByText('Saved Responses')).toBeInTheDocument();
    });
    
    // Check if the empty message is displayed
    expect(screen.getByText('No responses found.')).toBeInTheDocument();
  });
});
