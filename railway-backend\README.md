# Market Research Tool - Railway Backend

This is a clean FastAPI backend designed specifically for Railway deployment to handle LLM operations for the Market Research Tool.

## Architecture

- **Frontend**: Deployed on Vercel (handles UI and simple operations)
- **Backend**: Deployed on Railway (handles LLM-heavy operations)

## Features

- FastAPI with async support for better performance
- Dedicated LLM service modules for each AI provider
- Proper error handling and timeout management
- Health check endpoints for Railway monitoring
- CORS configured for Vercel frontend integration

## Supported LLM Providers

- **OpenAI** (GPT-4, GPT-3.5-turbo, etc.)
- **Google Gemini** (gemini-1.5-pro, etc.)
- **DeepSeek** (deepseek-chat)
- **Anthropic** (Claude models)
- **Groq** (Llama models)

## API Endpoints

- `GET /health` - Health check for Railway
- `POST /api/openai` - OpenAI completions
- `POST /api/gemini` - Google Gemini completions
- `POST /api/deepseek` - DeepSeek completions
- `POST /api/anthropic` - Anthropic completions
- `POST /api/groq` - Groq completions

## Request Format

All LLM endpoints accept the same request format:

```json
{
  "prompt": "Your question here",
  "model": "model-name",
  "thinkingMode": false
}
```

## Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. Run the server:
```bash
uvicorn main:app --reload --port 8000
```

## Railway Deployment

1. Create a new Railway project
2. Connect this `railway-backend` folder as the root
3. Set environment variables in Railway dashboard:
   - `OPENAI_API_KEY`
   - `GEMINI_API_KEY`
   - `DEEPSEEK_API_KEY`
   - `ANTHROPIC_API_KEY`
   - `GROQ_API_KEY`
4. Deploy!

Railway will automatically:
- Detect the Python project
- Install dependencies from requirements.txt
- Run the health check on `/health`
- Scale based on demand

## Environment Variables

Set these in your Railway project dashboard:

- `OPENAI_API_KEY` - Your OpenAI API key
- `GEMINI_API_KEY` - Your Google Gemini API key
- `DEEPSEEK_API_KEY` - Your DeepSeek API key
- `ANTHROPIC_API_KEY` - Your Anthropic API key
- `GROQ_API_KEY` - Your Groq API key

## Frontend Integration

Update your Vercel frontend's API base URL to point to your Railway deployment:

```javascript
const API_BASE_URL = 'https://your-railway-app.railway.app';
```

## Performance Notes

- Uses async/await for non-blocking operations
- 60-second timeout for LLM requests
- Automatic retries on Railway deployment failures
- Health checks ensure service availability
