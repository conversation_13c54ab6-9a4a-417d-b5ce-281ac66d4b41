// Simple healthcheck server that runs alongside the main application
const express = require('express');
const healthApp = express();
const HEALTH_PORT = process.env.HEALTH_PORT || 8080;

// Only handle specific healthcheck endpoints and not the root path
// This ensures we don't interfere with the main application
healthApp.get('/api/railway-health', (req, res) => {
  console.log('Railway healthcheck endpoint called at:', new Date().toISOString());
  res.status(200).json({ status: 'ok' });
});

// Start the healthcheck server on a different port
healthApp.listen(HEALTH_PORT, () => {
  console.log(`Healthcheck server running on port ${HEALTH_PORT}`);
  console.log('Healthcheck endpoint available at: /api/railway-health');
});
