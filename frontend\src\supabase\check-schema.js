import { supabase } from './client.js';

/**
 * Comprehensive check of the Supabase database schema
 * This will help diagnose issues with the questionnaire_responses table
 */
async function checkDatabaseSchema() {
  console.log('=== SUPABASE DATABASE SCHEMA CHECK ===');
  
  try {
    // 1. Check if user is authenticated
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Authentication error:', authError);
      return;
    }
    
    console.log('Authentication status:', authData.user ? 'Authenticated' : 'Not authenticated');
    if (authData.user) {
      console.log('User ID:', authData.user.id);
      console.log('User email:', authData.user.email);
    }
    
    // 2. Check if the questionnaire_responses table exists
    console.log('\n--- CHECKING IF TABLE EXISTS ---');
    const { data: tableData, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'questionnaire_responses')
      .eq('table_schema', 'public');
    
    if (tableError) {
      console.error('Error checking if table exists:', tableError);
      return;
    }
    
    if (!tableData || tableData.length === 0) {
      console.error('The questionnaire_responses table does not exist in the public schema');
      console.log('You need to create the table with:');
      console.log(`
CREATE TABLE public.questionnaire_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  questionnaire_type TEXT NOT NULL,
  questionnaire_name TEXT NOT NULL,
  responses JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
      `);
      return;
    }
    
    console.log('Table exists:', tableData);
    
    // 3. Check table columns
    console.log('\n--- CHECKING TABLE COLUMNS ---');
    const { data: columnData, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'questionnaire_responses')
      .eq('table_schema', 'public');
    
    if (columnError) {
      console.error('Error checking table columns:', columnError);
      return;
    }
    
    console.log('Table columns:');
    columnData.forEach(column => {
      console.log(`- ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not nullable'})`);
    });
    
    // Check if user_id column exists
    const hasUserIdColumn = columnData.some(col => col.column_name === 'user_id');
    console.log('Has user_id column:', hasUserIdColumn);
    
    if (!hasUserIdColumn) {
      console.log('You need to add the user_id column with:');
      console.log(`
ALTER TABLE questionnaire_responses 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
      `);
    }
    
    // 4. Check RLS policies
    console.log('\n--- CHECKING RLS POLICIES ---');
    const { data: rlsData, error: rlsError } = await supabase
      .rpc('get_policies_for_table', { table_name: 'questionnaire_responses' });
    
    if (rlsError) {
      console.error('Error checking RLS policies:', rlsError);
      // Try alternative approach
      console.log('Trying alternative approach to check RLS...');
      
      // Try to insert a test record to see if RLS blocks it
      const testRecord = {
        questionnaire_type: 'test',
        questionnaire_name: 'Test Questionnaire',
        responses: { test: 'test' },
        created_at: new Date().toISOString()
      };
      
      const { error: insertError } = await supabase
        .from('questionnaire_responses')
        .insert([testRecord]);
      
      if (insertError) {
        console.error('Test insert failed, possibly due to RLS:', insertError);
        
        if (insertError.message.includes('permission') || insertError.code === '42501') {
          console.log('This appears to be an RLS permission issue.');
          console.log('You may need to adjust your RLS policies to allow inserts.');
        }
      } else {
        console.log('Test insert succeeded, RLS is not blocking inserts.');
      }
    } else {
      console.log('RLS policies:', rlsData);
    }
    
    // 5. Count existing records
    console.log('\n--- CHECKING EXISTING RECORDS ---');
    const { data: records, error: recordsError, count } = await supabase
      .from('questionnaire_responses')
      .select('*', { count: 'exact' });
    
    if (recordsError) {
      console.error('Error fetching records:', recordsError);
      return;
    }
    
    console.log('Number of records:', records?.length || 0);
    
    if (records && records.length > 0) {
      console.log('Sample record:');
      console.log(records[0]);
    } else {
      console.log('No records found in the table.');
    }
    
    console.log('\n=== SCHEMA CHECK COMPLETE ===');
  } catch (err) {
    console.error('Exception during schema check:', err);
  }
}

// Run the check
checkDatabaseSchema().then(() => {
  console.log('Schema check completed. Check the console for results.');
});

export default checkDatabaseSchema;
