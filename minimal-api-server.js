// Robust API server for the Market Research Tool
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables with explicit path
console.log('Loading environment variables...');
try {
  const envPath = path.resolve(__dirname, '.env');
  console.log(`Loading .env file from: ${envPath}`);
  
  // Check if .env file exists and is readable
  if (fs.existsSync(envPath)) {
    const result = dotenv.config({ path: envPath });
    
    if (result.error) {
      console.error('Error loading .env file:', result.error);
    } else {
      console.log('.env file loaded successfully');
    }
  } else {
    console.error('.env file not found at path:', envPath);
  }
} catch (error) {
  console.error('Error during .env loading:', error);
}

// Normalize environment variables to handle different formats
function normalizeEnvVars() {
  // Gemini API key
  const geminiKey = process.env.GEMINI_API_KEY || process.env.google_api_key;
  if (geminiKey) {
    process.env.GEMINI_API_KEY = geminiKey;
    process.env.google_api_key = geminiKey;
  }
  
  // OpenAI API key
  const openaiKey = process.env.OPENAI_API_KEY || process.env.openai_api_key;
  if (openaiKey) {
    process.env.OPENAI_API_KEY = openaiKey;
    process.env.openai_api_key = openaiKey;
  }
  
  // DeepSeek API key
  const deepseekKey = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;
  if (deepseekKey) {
    process.env.DEEPSEEK_API_KEY = deepseekKey;
    process.env.deepseek_api_key = deepseekKey;
  }
}

// Run the normalization
normalizeEnvVars();

// Log environment variables status
console.log('Environment variables loaded:');
console.log(`- GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? 'Available' : 'Not available'}`);
console.log(`- OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? 'Available' : 'Not available'}`);
console.log(`- DEEPSEEK_API_KEY: ${process.env.DEEPSEEK_API_KEY ? 'Available' : 'Not available'}`);

// Create Express app
const app = express();
const PORT = 3001;

// Middleware
app.use(cors());

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Custom JSON body parser with safe error handling
app.use((req, res, next) => {
  express.json()(req, res, (err) => {
    if (err) {
      console.error('Invalid JSON in request body:', err.message);
      return res.status(400).json({ error: 'Invalid JSON in request body' });
    }
    next();
  });
});

app.use(express.urlencoded({ extended: true }));

// Status endpoint with HTML response
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Market Research API Server</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <h1>Market Research API Server</h1>
        <p>The API server is running successfully on port ${PORT}</p>
        
        <div class="endpoint">
          <h2>Available Endpoints:</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Environment Status:</h2>
          <ul>
            <li><strong>Gemini API Key:</strong> <span class="${process.env.GEMINI_API_KEY ? 'success' : 'error'}">${process.env.GEMINI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>OpenAI API Key:</strong> <span class="${process.env.OPENAI_API_KEY ? 'success' : 'error'}">${process.env.OPENAI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>DeepSeek API Key:</strong> <span class="${process.env.DEEPSEEK_API_KEY ? 'success' : 'error'}">${process.env.DEEPSEEK_API_KEY ? 'Available' : 'Not Available'}</span></li>
          </ul>
        </div>
      </body>
    </html>
  `);
});

// Enhanced API endpoints with proper error handling
app.post('/api/gemini', (req, res) => {
  try {
    console.log('Gemini API called');
    console.log('Request body:', req.body);
    
    const prompt = req.body.prompt || req.body.question || 'No prompt provided';
    const model = req.body.model || 'gemini-1.5-pro';
    
    console.log(`Processing Gemini request with model: ${model}`);
    
    // Return a mock response
    return res.status(200).json({
      text: `This is a response from the Gemini API using the ${model} model.\n\nYour prompt was: "${prompt}"`,
      model: model,
      success: true
    });
  } catch (error) {
    console.error('Error in Gemini handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

app.post('/api/openai', (req, res) => {
  try {
    console.log('OpenAI API called');
    console.log('Request body:', req.body);
    
    const prompt = req.body.prompt || req.body.question || 'No prompt provided';
    const model = req.body.model || 'gpt-3.5-turbo';
    
    console.log(`Processing OpenAI request with model: ${model}`);
    
    // Return a mock response
    return res.status(200).json({
      text: `This is a response from the OpenAI API using the ${model} model.\n\nYour prompt was: "${prompt}"`,
      model: model,
      success: true
    });
  } catch (error) {
    console.error('Error in OpenAI handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

app.post('/api/deepseek', (req, res) => {
  try {
    console.log('DeepSeek API called');
    console.log('Request body:', req.body);
    
    const prompt = req.body.prompt || req.body.question || 'No prompt provided';
    const model = req.body.model || 'deepseek-chat';
    
    console.log(`Processing DeepSeek request with model: ${model}`);
    
    // Return a mock response
    return res.status(200).json({
      text: `This is a response from the DeepSeek API using the ${model} model.\n\nYour prompt was: "${prompt}"`,
      model: model,
      success: true
    });
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Global error handler - must be added after all routes
app.use((err, req, res, next) => {
  console.error('Unhandled error in request:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'The server encountered an unexpected error. Please try again later.',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Process-level error handling to prevent crashes
process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION!');
  console.error(err.name, err.message, err.stack);
  // Log the error but keep the server running
});

process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION!');
  console.error(err.name, err.message, err.stack);
  // Log the error but keep the server running
});

// Create a file to indicate the server is running
fs.writeFileSync(path.join(__dirname, 'server-running.txt'), 'Server started at ' + new Date().toISOString());

// Update the file periodically to show the server is still running
setInterval(() => {
  fs.writeFileSync(path.join(__dirname, 'server-running.txt'), 'Server running at ' + new Date().toISOString());
  console.log('Server heartbeat check - still running at ' + new Date().toISOString());
}, 30000); // Update every 30 seconds

// Start server
const server = app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Gemini: http://localhost:${PORT}/api/gemini`);
  console.log(`- OpenAI: http://localhost:${PORT}/api/openai`);
  console.log(`- DeepSeek: http://localhost:${PORT}/api/deepseek`);
  console.log(`- Status page: http://localhost:${PORT}/`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

// Keep the process alive
process.stdin.resume();
