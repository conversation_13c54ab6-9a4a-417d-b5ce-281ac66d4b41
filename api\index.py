from http.server import BaseHTTPRequestHandler
from datetime import datetime
import json
import os
import sys
import re

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils import get_public_dir

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Extract path and query parameters
        path_parts = self.path.split('/')
        path_parts = [p for p in path_parts if p]  # Remove empty strings
        
        try:
            # Handle different endpoints based on path
            if len(path_parts) == 0:
                # Root API endpoint
                self._handle_root()
            elif len(path_parts) == 1 and path_parts[0] == 'yaml':
                # List YAML files (equivalent to yaml/list.py)
                self._handle_yaml_list()
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Not found"}).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def _handle_root(self):
        """Handle root API endpoint"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps({
            "message": "Welcome to the Market Research Tool API",
            "endpoints": [
                "/api/questionnaire/list",
                "/api/questionnaire/[file_id]",
                "/api/questionnaire/generate/[file_id]",
                "/api/questionnaire/save",
                "/api/responses/list",
                "/api/responses/[file_id]",
                "/api/responses/download/[file_id]",
                "/api/responses/save",
                "/api/yaml"
            ]
        }).encode())

    def _handle_yaml_list(self):
        """Handle listing YAML files"""
        try:
            public_dir = get_public_dir()
            print(f"Public directory path: {public_dir}")
            
            if os.path.exists(public_dir):
                print(f"Files in public directory: {os.listdir(public_dir)}")
                files = [f for f in os.listdir(public_dir) if f.endswith('.yaml') or f.endswith('.yml')]
            else:
                print("Public directory not found")
                files = []
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            self.wfile.write(json.dumps({"files": files}).encode())
            
        except Exception as e:
            print(f"Error in _handle_yaml_list: {str(e)}")
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return
