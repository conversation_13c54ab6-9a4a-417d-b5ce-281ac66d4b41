// Simple script to test environment variable loading
const dotenv = require('dotenv');
const path = require('path');

console.log('Current directory:', __dirname);
console.log('Attempting to load .env file...');

// Try to load with explicit path
const result = dotenv.config({ path: path.resolve(__dirname, '.env') });

if (result.error) {
  console.error('Error loading .env file:', result.error);
} else {
  console.log('.env file loaded successfully');
}

// Log all environment variables
console.log('\nAll environment variables:');
Object.keys(process.env).forEach(key => {
  // Only show the first few characters of API keys for security
  const value = key.toLowerCase().includes('api_key') || key.toLowerCase().includes('apikey') 
    ? process.env[key].substring(0, 5) + '...' 
    : process.env[key];
  
  console.log(`${key}: ${value}`);
});

// Check specific API keys
console.log('\nChecking specific API keys:');
console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Available' : 'Not available');
console.log('google_api_key:', process.env.google_api_key ? 'Available' : 'Not available');
console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Available' : 'Not available');
console.log('deepseek_api_key:', process.env.deepseek_api_key ? 'Available' : 'Not available');
console.log('anthropic_api_key:', process.env.anthropic_api_key ? 'Available' : 'Not available');
