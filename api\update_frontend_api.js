/**
 * Update Frontend API References
 * 
 * This script helps update frontend API references to point to the new FastAPI endpoints.
 * It scans the frontend code for API calls and suggests replacements.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const FRONTEND_DIR = path.join(__dirname, '../frontend');
const SRC_DIR = path.join(FRONTEND_DIR, 'src');
const API_PATTERNS = [
  { 
    pattern: /fetch\(['"]\/api\/([^'"]+)['"]/g, 
    replacement: 'fetch(\'/api/$1\''
  },
  { 
    pattern: /axios\.([a-z]+)\(['"]\/api\/([^'"]+)['"]/g, 
    replacement: 'axios.$1(\'/api/$2\''
  },
  {
    pattern: /const API_URL = ['"]([^'"]+)['"]/g,
    replacement: 'const API_URL = \'/api\''
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

/**
 * Find all JavaScript and TypeScript files in a directory
 * @param {string} dir Directory to search
 * @param {Array} fileList Array to store found files
 * @returns {Array} List of found files
 */
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('node_modules') && !file.startsWith('.')) {
      findJsFiles(filePath, fileList);
    } else if (
      stat.isFile() && 
      (file.endsWith('.js') || file.endsWith('.jsx') || file.endsWith('.ts') || file.endsWith('.tsx'))
    ) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

/**
 * Scan a file for API patterns
 * @param {string} filePath Path to the file
 * @returns {Array} Found matches
 */
function scanFileForApiCalls(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const matches = [];
  
  API_PATTERNS.forEach(({ pattern }) => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      matches.push({
        file: filePath,
        line: getLineNumber(content, match.index),
        match: match[0],
        endpoint: match[1] || match[0]
      });
    }
  });
  
  return matches;
}

/**
 * Get line number for a position in text
 * @param {string} text Text content
 * @param {number} position Position in text
 * @returns {number} Line number
 */
function getLineNumber(text, position) {
  const lines = text.substring(0, position).split('\n');
  return lines.length;
}

/**
 * Main function
 */
function main() {
  console.log(`${colors.bright}Market Research Tool - Frontend API Update Helper${colors.reset}`);
  console.log('=============================================\n');
  
  // Check if frontend directory exists
  if (!fs.existsSync(FRONTEND_DIR)) {
    console.error(`${colors.red}Error: Frontend directory not found at ${FRONTEND_DIR}${colors.reset}`);
    process.exit(1);
  }
  
  console.log(`${colors.cyan}Scanning frontend code for API references...${colors.reset}`);
  const jsFiles = findJsFiles(SRC_DIR);
  console.log(`Found ${jsFiles.length} JavaScript/TypeScript files to scan.\n`);
  
  let totalMatches = 0;
  const fileMatches = {};
  
  // Scan each file for API calls
  jsFiles.forEach(file => {
    const matches = scanFileForApiCalls(file);
    if (matches.length > 0) {
      fileMatches[file] = matches;
      totalMatches += matches.length;
    }
  });
  
  // Print results
  if (totalMatches === 0) {
    console.log(`${colors.green}No API references found that need updating.${colors.reset}`);
    return;
  }
  
  console.log(`${colors.yellow}Found ${totalMatches} API references in ${Object.keys(fileMatches).length} files:${colors.reset}\n`);
  
  Object.entries(fileMatches).forEach(([file, matches]) => {
    const relativePath = path.relative(process.cwd(), file);
    console.log(`${colors.bright}${relativePath}${colors.reset}`);
    
    matches.forEach(match => {
      console.log(`  Line ${match.line}: ${colors.yellow}${match.match}${colors.reset}`);
    });
    console.log('');
  });
  
  console.log(`${colors.bright}Next Steps:${colors.reset}`);
  console.log(`1. The FastAPI endpoints use the same URL structure as your current serverless functions`);
  console.log(`2. No changes are needed if your frontend uses relative URLs (e.g., '/api/questionnaire/list')`);
  console.log(`3. If you're using absolute URLs, update them to point to your new FastAPI deployment`);
  console.log(`4. Test your application with the new API endpoints\n`);
  
  console.log(`${colors.green}Your FastAPI implementation is ready to use!${colors.reset}`);
}

// Run the main function
main();
