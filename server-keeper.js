// Server Keeper - A script to keep the API server running
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const SERVER_SCRIPT = path.join(__dirname, 'minimal-api-server.js');
const LOG_FILE = path.join(__dirname, 'api-server.log');
const STATUS_FILE = path.join(__dirname, 'server-status.json');
const MAX_RESTARTS = 10;
const RESTART_DELAY = 5000; // 5 seconds

// Initialize counters
let restartCount = 0;
let serverProcess = null;
let isShuttingDown = false;

// Create or update status file
function updateStatus(status) {
  const statusData = {
    status,
    lastUpdated: new Date().toISOString(),
    restartCount,
    pid: serverProcess ? serverProcess.pid : null
  };
  
  fs.writeFileSync(STATUS_FILE, JSON.stringify(statusData, null, 2));
  console.log(`[Keeper] Server status: ${status}`);
}

// Start the server as a child process
function startServer() {
  console.log(`[Keeper] Starting server (attempt ${restartCount + 1}/${MAX_RESTARTS})...`);
  updateStatus('starting');
  
  // Create log file stream
  const logStream = fs.createWriteStream(LOG_FILE, { flags: 'a' });
  
  // Spawn the server process
  serverProcess = spawn('node', [SERVER_SCRIPT], {
    stdio: ['ignore', 'pipe', 'pipe'],
    detached: false
  });
  
  // Log the PID
  console.log(`[Keeper] Server started with PID: ${serverProcess.pid}`);
  
  // Pipe stdout and stderr to log file
  serverProcess.stdout.pipe(logStream);
  serverProcess.stderr.pipe(logStream);
  
  // Also log to console
  serverProcess.stdout.on('data', (data) => {
    console.log(`[Server] ${data.toString().trim()}`);
  });
  
  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error] ${data.toString().trim()}`);
  });
  
  // Handle server exit
  serverProcess.on('exit', (code, signal) => {
    const exitReason = signal ? `signal ${signal}` : `exit code ${code}`;
    console.log(`[Keeper] Server process exited with ${exitReason}`);
    
    // Close log stream
    logStream.end();
    
    // Restart server if not shutting down
    if (!isShuttingDown) {
      restartCount++;
      
      if (restartCount < MAX_RESTARTS) {
        console.log(`[Keeper] Restarting server in ${RESTART_DELAY/1000} seconds...`);
        updateStatus('restarting');
        setTimeout(startServer, RESTART_DELAY);
      } else {
        console.log('[Keeper] Maximum restart attempts reached. Giving up.');
        updateStatus('failed');
        process.exit(1);
      }
    }
  });
  
  // Update status to running
  updateStatus('running');
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('[Keeper] Received SIGINT. Shutting down gracefully...');
  shutdown();
});

process.on('SIGTERM', () => {
  console.log('[Keeper] Received SIGTERM. Shutting down gracefully...');
  shutdown();
});

// Graceful shutdown
function shutdown() {
  isShuttingDown = true;
  updateStatus('stopping');
  
  if (serverProcess) {
    console.log('[Keeper] Stopping server process...');
    
    // Try to kill the process gracefully
    serverProcess.kill('SIGTERM');
    
    // Force kill after timeout
    setTimeout(() => {
      if (serverProcess) {
        console.log('[Keeper] Force killing server process...');
        serverProcess.kill('SIGKILL');
      }
      
      updateStatus('stopped');
      process.exit(0);
    }, 5000);
  } else {
    updateStatus('stopped');
    process.exit(0);
  }
}

// Keep the process alive
process.stdin.resume();

// Start the server
console.log('[Keeper] Server keeper started');
startServer();

// Log heartbeat every minute
setInterval(() => {
  if (serverProcess) {
    console.log(`[Keeper] Heartbeat - Server running with PID: ${serverProcess.pid}`);
    updateStatus('running');
  }
}, 60000);

console.log('[Keeper] Press Ctrl+C to stop the server');
