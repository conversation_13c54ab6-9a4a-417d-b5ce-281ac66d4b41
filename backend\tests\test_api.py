import os
import json
import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app
from app.models import QuestionnaireResponse

client = TestClient(app)

# Test data
test_questionnaire_response = {
    "questionnaire": "Test Questionnaire",
    "responses": {
        "q1": "Test answer 1",
        "q2": ["Option A", "Option B"],
        "q3": 5
    }
}

@pytest.fixture(scope="module")
def cleanup():
    """Cleanup test files after tests run"""
    yield
    # Clean up any test files created during testing
    response_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "responses")
    if os.path.exists(response_dir):
        for file in os.listdir(response_dir):
            if file.startswith("test_questionnaire_"):
                os.remove(os.path.join(response_dir, file))


def test_list_yaml_files():
    """Test the endpoint to list YAML questionnaire files"""
    response = client.get("/api/yaml/list")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    # Check if the response contains yaml files
    yaml_files = [file for file in response.json() if file.endswith('.yaml')]
    assert len(yaml_files) > 0


def test_save_response(cleanup):
    """Test saving a questionnaire response"""
    response = client.post("/api/responses/save", json=test_questionnaire_response)
    assert response.status_code == 200
    assert response.json()["success"] is True
    assert "id" in response.json()
    assert "file" in response.json()
    
    # Store the response ID for later tests
    return response.json()["id"]


def test_list_responses():
    """Test listing all responses"""
    response = client.get("/api/responses/list")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_get_response():
    """Test getting a specific response"""
    # First save a response to get an ID
    save_response = client.post("/api/responses/save", json=test_questionnaire_response)
    response_id = save_response.json()["id"]
    
    # Then retrieve that response
    response = client.get(f"/api/responses/{response_id}")
    assert response.status_code == 200
    assert response.json()["id"] == response_id
    assert response.json()["questionnaire"] == test_questionnaire_response["questionnaire"]
    
    # Test with invalid ID
    response = client.get("/api/responses/invalid_id")
    assert response.status_code == 404


def test_download_response():
    """Test downloading a response"""
    # First save a response to get an ID
    save_response = client.post("/api/responses/save", json=test_questionnaire_response)
    response_id = save_response.json()["id"]
    
    # Then download that response
    response = client.get(f"/api/responses/download/{response_id}")
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    assert "attachment" in response.headers["content-disposition"]
    
    # Test with invalid ID
    response = client.get("/api/responses/download/invalid_id")
    assert response.status_code == 404


def test_pydantic_model():
    """Test the QuestionnaireResponse Pydantic model"""
    response_data = {
        "questionnaire": "Test Questionnaire",
        "responses": {
            "q1": "Test answer",
            "q2": ["Option A", "Option B"]
        }
    }
    
    # Create a model instance
    model = QuestionnaireResponse(**response_data)
    
    # Check that the model has the expected fields
    assert model.questionnaire == "Test Questionnaire"
    assert model.responses["q1"] == "Test answer"
    assert model.responses["q2"] == ["Option A", "Option B"]
    assert model.id is not None  # Should be auto-generated
    assert model.timestamp is not None  # Should be auto-generated
