import re
from typing import List
import os
import json
from .models import Questionnaire, Question

def parse_markdown_to_questionnaire(md_text: str, title: str = "Questionnaire") -> Questionnaire:
    """
    Parse a Markdown file to extract questionnaire sections and questions.

    Args:
        md_text (str): The Markdown file content.
        title (str): Title for the questionnaire.

    Returns:
        Questionnaire: Parsed questionnaire object.
    """
    # Example: Look for sections like '## Questionnaire Considerations' and list items
    question_blocks = re.findall(r'##+\s*Questionnaire Considerations[\s\S]*?(?=^## |\Z)', md_text, re.MULTILINE)
    questions: List[Question] = []
    for block in question_blocks:
        # Find markdown list items (e.g., '- What is your age?')
        for match in re.findall(r'^[-*+]\s+(.*)', block, re.MULTILINE):
            questions.append(Question(text=match.strip()))
    # Fallback: If no block found, try to extract all list items as questions
    if not questions:
        for match in re.findall(r'^[-*+]\s+(.*)', md_text, re.MULTILINE):
            questions.append(Question(text=match.strip()))
    return Questionnaire(title=title, questions=questions)

def get_data_dir():
    """Get the data directory path for serverless functions."""
    # For Vercel, we'll use /tmp directory for temporary storage
    # In production, you might want to use a database or cloud storage
    data_dir = "/tmp/data"
    os.makedirs(data_dir, exist_ok=True)
    return data_dir

def get_questionnaires_dir():
    """Get the questionnaires directory path."""
    questionnaires_dir = os.path.join(get_data_dir(), "questionnaires")
    os.makedirs(questionnaires_dir, exist_ok=True)
    return questionnaires_dir

def get_responses_dir():
    """Get the responses directory path."""
    responses_dir = os.path.join(get_data_dir(), "responses")
    os.makedirs(responses_dir, exist_ok=True)
    return responses_dir

def get_public_dir():
    """Get the public directory path."""
    # Try different possible locations for the public directory
    possible_paths = [
        # Standard local development path
        os.path.abspath(os.path.join(os.path.dirname(__file__), '../frontend/public')),
        # Vercel deployment path
        '/var/task/frontend/public',
        # Root public directory
        '/var/task/public',
        # Dist directory
        '/var/task/frontend/dist/public',
        # Root level
        '/var/task'
    ]
    
    for path in possible_paths:
        print(f"Checking path: {path}")
        if os.path.exists(path):
            print(f"Found directory at: {path}")
            try:
                files = os.listdir(path)
                print(f"Files in directory: {files}")
                yaml_files = [f for f in files if f.endswith('.yaml') or f.endswith('.yml')]
                if yaml_files:
                    print(f"YAML files found: {yaml_files}")
                    return path
            except Exception as e:
                print(f"Error listing directory {path}: {str(e)}")
    
    # Default to the first path even if it doesn't exist
    print(f"No valid directory with YAML files found, using default path")
    return possible_paths[0]
