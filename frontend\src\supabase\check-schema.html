<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Schema Check</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #3b82f6;
    }
    button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 0;
    }
    button:hover {
      background-color: #2563eb;
    }
    pre {
      background-color: #f1f5f9;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      white-space: pre-wrap;
      margin: 15px 0;
    }
    .log-container {
      margin-top: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 5px;
      padding: 10px;
      max-height: 500px;
      overflow-y: auto;
    }
    .log-entry {
      margin-bottom: 5px;
      border-bottom: 1px solid #f1f5f9;
      padding-bottom: 5px;
    }
    .error {
      color: #dc2626;
    }
    .success {
      color: #16a34a;
    }
    .info {
      color: #2563eb;
    }
  </style>
</head>
<body>
  <h1>Supabase Database Schema Check</h1>
  <p>This tool will check your Supabase database schema to help diagnose issues with the questionnaire_responses table.</p>
  
  <div>
    <button id="runCheck">Run Schema Check</button>
    <button id="clearLogs">Clear Logs</button>
  </div>
  
  <div class="log-container" id="logContainer">
    <div class="log-entry info">Click "Run Schema Check" to begin...</div>
  </div>
  
  <script type="module">
    import { supabase } from './client.js';
    
    const logContainer = document.getElementById('logContainer');
    const runCheckButton = document.getElementById('runCheck');
    const clearLogsButton = document.getElementById('clearLogs');
    
    // Log function to display in the UI
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry ${type}`;
      logEntry.textContent = message;
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
      console.log(message);
    }
    
    // Clear logs
    clearLogsButton.addEventListener('click', () => {
      logContainer.innerHTML = '';
      log('Logs cleared', 'info');
    });
    
    // Run the schema check
    runCheckButton.addEventListener('click', async () => {
      log('=== SUPABASE DATABASE SCHEMA CHECK ===', 'info');
      runCheckButton.disabled = true;
      
      try {
        // 1. Check if user is authenticated
        log('\n--- CHECKING AUTHENTICATION ---', 'info');
        const { data: authData, error: authError } = await supabase.auth.getUser();
        
        if (authError) {
          log('Authentication error: ' + authError.message, 'error');
          return;
        }
        
        log('Authentication status: ' + (authData.user ? 'Authenticated' : 'Not authenticated'), authData.user ? 'success' : 'error');
        if (authData.user) {
          log('User ID: ' + authData.user.id, 'info');
          log('User email: ' + authData.user.email, 'info');
        } else {
          log('You need to be logged in to save questionnaire responses to Supabase', 'error');
        }
        
        // 2. Check if the questionnaire_responses table exists
        log('\n--- CHECKING IF TABLE EXISTS ---', 'info');
        const { data: tableData, error: tableError } = await supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_name', 'questionnaire_responses')
          .eq('table_schema', 'public');
        
        if (tableError) {
          log('Error checking if table exists: ' + tableError.message, 'error');
          return;
        }
        
        if (!tableData || tableData.length === 0) {
          log('The questionnaire_responses table does not exist in the public schema', 'error');
          log('You need to create the table with:', 'info');
          log(`
CREATE TABLE public.questionnaire_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  questionnaire_type TEXT NOT NULL,
  questionnaire_name TEXT NOT NULL,
  responses JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`, 'info');
          return;
        }
        
        log('Table exists: ' + JSON.stringify(tableData), 'success');
        
        // 3. Check table columns
        log('\n--- CHECKING TABLE COLUMNS ---', 'info');
        const { data: columnData, error: columnError } = await supabase
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_name', 'questionnaire_responses')
          .eq('table_schema', 'public');
        
        if (columnError) {
          log('Error checking table columns: ' + columnError.message, 'error');
          return;
        }
        
        log('Table columns:', 'info');
        columnData.forEach(column => {
          log(`- ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not nullable'})`, 'info');
        });
        
        // Check if user_id column exists
        const hasUserIdColumn = columnData.some(col => col.column_name === 'user_id');
        log('Has user_id column: ' + hasUserIdColumn, hasUserIdColumn ? 'success' : 'error');
        
        if (!hasUserIdColumn) {
          log('You need to add the user_id column with:', 'info');
          log(`
ALTER TABLE questionnaire_responses 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;`, 'info');
        }
        
        // 4. Try to count existing records
        log('\n--- CHECKING EXISTING RECORDS ---', 'info');
        const { data: records, error: recordsError, count } = await supabase
          .from('questionnaire_responses')
          .select('*', { count: 'exact' });
        
        if (recordsError) {
          log('Error fetching records: ' + recordsError.message, 'error');
          
          if (recordsError.message.includes('permission') || recordsError.code === '42501') {
            log('This appears to be an RLS permission issue.', 'error');
            log('You may need to adjust your RLS policies to allow selects.', 'info');
          }
          
          return;
        }
        
        log('Number of records: ' + (records?.length || 0), 'info');
        
        if (records && records.length > 0) {
          log('Sample record:', 'success');
          log(JSON.stringify(records[0], null, 2), 'info');
        } else {
          log('No records found in the table.', 'error');
        }
        
        // 5. Try a test insert
        log('\n--- TESTING INSERT ---', 'info');
        const testRecord = {
          questionnaire_type: 'test_schema_check',
          questionnaire_name: 'Schema Check Test',
          responses: { test_question: 'test_answer' },
          created_at: new Date().toISOString()
        };
        
        log('Attempting to insert test record: ' + JSON.stringify(testRecord), 'info');
        
        const { data: insertData, error: insertError } = await supabase
          .from('questionnaire_responses')
          .insert([testRecord])
          .select();
        
        if (insertError) {
          log('Test insert failed: ' + insertError.message, 'error');
          
          if (insertError.message.includes('permission') || insertError.code === '42501') {
            log('This appears to be an RLS permission issue.', 'error');
            log('You may need to adjust your RLS policies to allow inserts.', 'info');
          } else if (insertError.message.includes('user_id')) {
            log('This error is related to the user_id column. The temporary fix should resolve this.', 'error');
          }
        } else {
          log('Test insert succeeded!', 'success');
          log('Inserted record: ' + JSON.stringify(insertData), 'info');
        }
        
        log('\n=== SCHEMA CHECK COMPLETE ===', 'success');
      } catch (err) {
        log('Exception during schema check: ' + err.message, 'error');
        console.error(err);
      } finally {
        runCheckButton.disabled = false;
      }
    });
  </script>
</body>
</html>
