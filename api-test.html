<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Server Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
    }
    .endpoint {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .endpoint h2 {
      margin-top: 0;
    }
    .status {
      padding: 5px 10px;
      border-radius: 3px;
      font-weight: bold;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .pending {
      background-color: #fff3cd;
      color: #856404;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #0069d9;
    }
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>API Server Test</h1>
  <p>This page tests the API server endpoints for different LLM models.</p>
  
  <div class="endpoint">
    <h2>Gemini API</h2>
    <div>
      <span>Status: </span>
      <span id="gemini-status" class="status pending">Pending</span>
    </div>
    <button onclick="testGemini()">Test Gemini API</button>
    <div id="gemini-response"></div>
  </div>
  
  <div class="endpoint">
    <h2>OpenAI API</h2>
    <div>
      <span>Status: </span>
      <span id="openai-status" class="status pending">Pending</span>
    </div>
    <button onclick="testOpenAI()">Test OpenAI API</button>
    <div id="openai-response"></div>
  </div>
  
  <div class="endpoint">
    <h2>DeepSeek API</h2>
    <div>
      <span>Status: </span>
      <span id="deepseek-status" class="status pending">Pending</span>
    </div>
    <button onclick="testDeepSeek()">Test DeepSeek API</button>
    <div id="deepseek-response"></div>
  </div>

  <script>
    const BASE_URL = 'http://localhost:3000';
    
    async function testEndpoint(endpoint, model, statusId, responseId) {
      const statusEl = document.getElementById(statusId);
      const responseEl = document.getElementById(responseId);
      
      statusEl.textContent = 'Testing...';
      statusEl.className = 'status pending';
      responseEl.innerHTML = '';
      
      try {
        const response = await fetch(`${BASE_URL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            prompt: 'Hello, this is a test prompt. Please respond with a short confirmation.',
            model: model
          })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        statusEl.textContent = 'Success';
        statusEl.className = 'status success';
        
        responseEl.innerHTML = `<h3>Response:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
      } catch (error) {
        statusEl.textContent = 'Error';
        statusEl.className = 'status error';
        responseEl.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
      }
    }
    
    function testGemini() {
      testEndpoint('/api/gemini', 'gemini-pro', 'gemini-status', 'gemini-response');
    }
    
    function testOpenAI() {
      testEndpoint('/api/openai', 'gpt-3.5-turbo', 'openai-status', 'openai-response');
    }
    
    function testDeepSeek() {
      testEndpoint('/api/deepseek', 'deepseek-chat', 'deepseek-status', 'deepseek-response');
    }
  </script>
</body>
</html>
