import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '../supabase/client';
import { UserProfileService } from '../services/userProfileService';

// Create the auth context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fallbackMode, setFallbackMode] = useState(false);
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileCache, setProfileCache] = useState(new Map()); // Cache for user profiles

  // Load user profile with caching
  const loadUserProfile = async (userId, forceRefresh = false) => {
    // Check cache first (unless force refresh)
    if (!forceRefresh && profileCache.has(userId)) {
      const cachedProfile = profileCache.get(userId);
      console.log('📋 Using cached user profile for userId:', userId);
      setUserProfile(cachedProfile);
      return cachedProfile;
    }

    // Prevent multiple simultaneous profile loads
    if (profileLoading) {
      console.log('⏳ Profile already loading, skipping duplicate request');
      return;
    }

    try {
      setProfileLoading(true);
      console.log('🔍 Loading user profile for userId:', userId);

      const profile = await UserProfileService.getUserProfile(userId);

      // Cache the profile
      setProfileCache(prev => new Map(prev.set(userId, profile)));
      setUserProfile(profile);

      console.log('✅ User profile loaded successfully:', profile);
      console.log('🔑 User role:', profile?.role);
      console.log('👑 Is admin:', profile?.role === 'admin');

      return profile;
    } catch (error) {
      console.error('❌ Error loading user profile:', error);
      console.error('Error details:', error.message, error.code);
      // Set a fallback profile to prevent infinite loading
      setUserProfile(null);
      return null;
    } finally {
      setProfileLoading(false);
    }
  };

  // Check if user is admin
  const isAdmin = () => {
    const result = userProfile?.role === 'admin';
    console.log('🔍 isAdmin check:', {
      userProfile: userProfile,
      role: userProfile?.role,
      isAdmin: result
    });
    return result;
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    if (!userProfile) return false;
    if (userProfile.role === 'admin') return true;
    return userProfile.permissions?.[permission] === true;
  };

  // Check for user session on initial load
  useEffect(() => {
    let loadingTimeout;

    async function getInitialSession() {
      try {
        setLoading(true);

        // Set a timeout to prevent infinite loading
        loadingTimeout = setTimeout(() => {
          console.warn('Loading timeout reached, enabling fallback mode');
          setLoading(false);
          setFallbackMode(true);
          setUser({ id: 'fallback-user', email: '<EMAIL>', fallback: true });
        }, 30000); // 30 second timeout (increased from 10)

        // Test Supabase connection first
        console.log('Testing Supabase connection...');
        console.log('Supabase URL:', supabase.supabaseUrl);
        console.log('Supabase Key (first 20 chars):', supabase.supabaseKey?.substring(0, 20) + '...');

        // Get current session
        console.log('Getting current session...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Session error:', sessionError);
          throw sessionError;
        }

        console.log('Session result:', session ? 'Session found' : 'No session');

        if (session) {
          const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

          if (userError) {
            throw userError;
          }

          setUser(currentUser);
          // Load user profile
          try {
            await loadUserProfile(currentUser.id);
          } catch (profileError) {
            console.error('Profile loading failed, continuing without profile:', profileError);
            // Continue without profile to prevent infinite loading
          }
        }
      } catch (err) {
        console.error('Error getting initial session:', err);
        console.log('Enabling fallback mode - app will work without authentication');
        setError(err.message);
        setFallbackMode(true);
        // In fallback mode, create a mock user so the app works
        setUser({ id: 'fallback-user', email: '<EMAIL>', fallback: true });
      } finally {
        if (loadingTimeout) clearTimeout(loadingTimeout); // Clear the timeout
        setLoading(false);
      }
    }

    getInitialSession();
    
    // Set up auth state change listener (only if not in fallback mode)
    let subscription = null;
    if (!fallbackMode) {
      try {
        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('Auth state changed:', event, session);

            if (session) {
              setUser(session.user);
              // Load user profile
              try {
                await loadUserProfile(session.user.id);
              } catch (profileError) {
                console.error('Profile loading failed in auth listener:', profileError);
                // Continue without profile
              }
            } else {
              setUser(null);
              setUserProfile(null);
            }

            setLoading(false);
          }
        );
        subscription = authSubscription;
      } catch (err) {
        console.error('Error setting up auth listener:', err);
      }
    }
    
    // Clean up subscription on unmount
    return () => {
      if (subscription) subscription.unsubscribe();
    };
  }, []);
  
  // Sign in with email and password
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing in:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign up with email and password
  const signUp = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing up:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset password
  const resetPassword = async (email) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error resetting password:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Update user profile
  const updateProfile = async (updates) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.updateUser(updates);
      
      if (error) {
        throw error;
      }
      
      setUser(data.user);
      return data;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Context value
  const value = {
    user,
    userProfile,
    loading,
    error,
    fallbackMode,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    setError,
    isAdmin,
    hasPermission,
    loadUserProfile
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
