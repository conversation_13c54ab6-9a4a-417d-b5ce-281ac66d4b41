import React, { useState, useEffect } from 'react';
import { supabase, saveQuestionnaireResponses } from '../supabase/client';
import testSaveResponse from '../supabase/debug-save';

function SupabaseTest() {
  const [connectionStatus, setConnectionStatus] = useState('Testing...');
  const [responseCount, setResponseCount] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [testResult, setTestResult] = useState(null);
  const [testResponses, setTestResponses] = useState([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    async function testConnection() {
      try {
        console.log('Testing connection to Supabase...');
        
        // Test simple query to verify connection
        const { data, error, count } = await supabase
          .from('questionnaire_responses')
          .select('*', { count: 'exact' });
        
        if (error) {
          console.error('Error connecting to Supabase:', error);
          setConnectionStatus('Failed');
          setError(error.message);
          return;
        }
        
        console.log('Successfully connected to Supabase!');
        console.log('Current number of questionnaire responses:', data.length);
        setConnectionStatus('Connected');
        setResponseCount(data.length);
      } catch (err) {
        console.error('Exception when connecting to Supabase:', err);
        setConnectionStatus('Failed');
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    
    testConnection();
  }, []);

  // Function to run the test save
  const runTestSave = async () => {
    setSaving(true);
    setTestResult(null);
    try {
      console.log('Starting test save process...');
      
      // First check if the table exists
      console.log('Checking if questionnaire_responses table exists...');
      const { data: tableData, error: tableError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_name', 'questionnaire_responses')
        .eq('table_schema', 'public');
      
      if (tableError) {
        console.error('Error checking if table exists:', tableError);
        setTestResult({
          success: false,
          message: `Error checking if table exists: ${tableError.message}`,
          error: tableError
        });
        setSaving(false);
        return;
      }
      
      console.log('Table check result:', tableData);
      
      if (!tableData || tableData.length === 0) {
        const errorMsg = 'The questionnaire_responses table does not exist in the public schema';
        console.error(errorMsg);
        setTestResult({
          success: false,
          message: errorMsg
        });
        setSaving(false);
        return;
      }
      
      // Check table columns
      console.log('Checking table columns...');
      const { data: columnData, error: columnError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'questionnaire_responses')
        .eq('table_schema', 'public');
      
      if (columnError) {
        console.error('Error checking table columns:', columnError);
        setTestResult({
          success: false,
          message: `Error checking table columns: ${columnError.message}`,
          error: columnError
        });
        setSaving(false);
        return;
      }
      
      console.log('Table columns:', columnData?.map(col => col.column_name));
      
      // Get current user
      console.log('Getting current user...');
      const { data: authData } = await supabase.auth.getUser();
      const user = authData?.user;
      
      console.log('Current user:', user ? `ID: ${user.id}, Email: ${user.email}` : 'Not authenticated');
      
      if (!user) {
        setTestResult({
          success: false,
          message: 'You must be logged in to test saving responses.'
        });
        setSaving(false);
        return;
      }
      
      // Test data
      const testData = {
        questionnaireType: 'consumerInsight',
        questionnaireName: 'Consumer Insights',
        responses: {
          test_question_1: 'Test answer 1',
          test_question_2: 'Test answer 2'
        }
      };
      
      console.log('Attempting to save test data:', testData);
      
      // Try to save
      const { data, error } = await saveQuestionnaireResponses(
        testData.questionnaireType,
        testData.questionnaireName,
        testData.responses
      );
      
      console.log('Save response:', { data, error });
      
      if (error) {
        setTestResult({
          success: false,
          message: `Error saving test response: ${error.message}`,
          error
        });
      } else {
        setTestResult({
          success: true,
          message: 'Successfully saved test response!'
        });
        
        // Refresh the response count
        console.log('Refreshing responses after successful save...');
        await fetchResponses();
      }
    } catch (err) {
      console.error('Exception in runTestSave:', err);
      setTestResult({
        success: false,
        message: `Exception when testing save response: ${err.message}`,
        error: err
      });
    } finally {
      setSaving(false);
    }
  };
  
  // Function to fetch all responses
  const fetchResponses = async () => {
    try {
      console.log('Fetching questionnaire responses...');
      
      // First check if the table exists
      const { data: tableData, error: tableError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_name', 'questionnaire_responses')
        .eq('table_schema', 'public');
      
      if (tableError) {
        console.error('Error checking if table exists:', tableError);
        setError('Error checking if table exists: ' + tableError.message);
        return;
      }
      
      console.log('Table check result:', tableData);
      
      if (!tableData || tableData.length === 0) {
        console.error('The questionnaire_responses table does not exist in the public schema');
        setError('The questionnaire_responses table does not exist in the public schema');
        return;
      }
      
      // Now fetch the responses
      const { data, error } = await supabase
        .from('questionnaire_responses')
        .select('*')
        .order('created_at', { ascending: false });
      
      console.log('Fetch responses result:', { data, error });
        
      if (error) {
        console.error('Error fetching responses:', error);
        setError('Error fetching responses: ' + error.message);
      } else {
        console.log('Successfully fetched responses:', data);
        setTestResponses(data || []);
        setResponseCount(data?.length || 0);
      }
    } catch (err) {
      console.error('Exception fetching responses:', err);
      setError('Exception fetching responses: ' + err.message);
    }
  };
  
  // Call fetchResponses when component mounts
  useEffect(() => {
    if (connectionStatus === 'Connected') {
      fetchResponses();
    }
  }, [connectionStatus]);

  return (
    <div className="p-6 max-w-4xl mx-auto bg-white rounded-xl shadow-md mt-10">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="mb-4">
            <p className="font-semibold">Connection Status:</p>
            <div className={`mt-1 p-2 rounded ${
              connectionStatus === 'Connected' ? 'bg-green-100 text-green-800' : 
              connectionStatus === 'Failed' ? 'bg-red-100 text-red-800' : 
              'bg-yellow-100 text-yellow-800'
            }`}>
              {connectionStatus}
            </div>
          </div>
          
          {responseCount !== null && (
            <div className="mb-4">
              <p className="font-semibold">Questionnaire Responses in Database:</p>
              <p className="mt-1 p-2 bg-blue-100 text-blue-800 rounded">{responseCount}</p>
            </div>
          )}
          
          {error && (
            <div className="mb-4">
              <p className="font-semibold">Error:</p>
              <p className="mt-1 p-2 bg-red-100 text-red-800 rounded">{error}</p>
            </div>
          )}
          
          <div className="mt-6">
            <h2 className="text-xl font-semibold mb-2">Connection Details</h2>
            <div className="bg-gray-100 p-3 rounded">
              <p><span className="font-medium">URL:</span> {import.meta.env.VITE_SUPABASE_URL}</p>
              <p><span className="font-medium">Table:</span> questionnaire_responses</p>
              <p><span className="font-medium">Environment:</span> {import.meta.env.MODE}</p>
            </div>
          </div>
          
          <div className="mt-6">
            <button 
              onClick={runTestSave}
              disabled={saving || connectionStatus !== 'Connected'}
              className={`px-4 py-2 rounded font-medium ${
                saving ? 'bg-gray-300 text-gray-500' : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              {saving ? 'Saving Test Data...' : 'Test Save Questionnaire Response'}
            </button>
            
            {testResult && (
              <div className={`mt-3 p-3 rounded ${testResult.success ? 'bg-green-100' : 'bg-red-100'}`}>
                <p className={testResult.success ? 'text-green-800' : 'text-red-800'}>
                  {testResult.message}
                </p>
                {testResult.error && (
                  <div className="mt-2">
                    <p className="font-medium">Error Code: {testResult.error.code || 'N/A'}</p>
                    <p className="font-medium">Error Details: {testResult.error.details || 'N/A'}</p>
                    <p className="font-medium">Error Hint: {testResult.error.hint || 'N/A'}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-2">Existing Responses</h2>
          <div className="overflow-auto max-h-96 bg-gray-50 p-3 rounded">
            {testResponses.length > 0 ? (
              testResponses.map((response, index) => (
                <div key={index} className="mb-4 p-3 bg-white rounded shadow">
                  <p><span className="font-medium">Type:</span> {response.questionnaire_type}</p>
                  <p><span className="font-medium">Name:</span> {response.questionnaire_name}</p>
                  <p><span className="font-medium">Created:</span> {new Date(response.created_at).toLocaleString()}</p>
                  <p><span className="font-medium">User ID:</span> {response.user_id || 'None'}</p>
                  <details className="mt-2">
                    <summary className="cursor-pointer font-medium text-blue-600">View Responses</summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs">
                      {JSON.stringify(response.responses, null, 2)}
                    </pre>
                  </details>
                </div>
              ))
            ) : (
              <p className="text-gray-500">No responses found in database.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SupabaseTest;
