import React, { useState } from 'react';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';

function CompetitionAnalysis() {
  const [showQuestionnaire, setShowQuestionnaire] = useState(false);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  
  // Handle questionnaire submission
  const handleSubmit = (data) => {
    console.log('Questionnaire submitted:', data);
    setSubmissionResult({
      success: true,
      message: 'Thank you for your submission!',
      data: data
    });
    setShowQuestionnaire(false);
  };
  
  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);
    
    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your competitive analysis strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
      setShowQuestionnaire(false);
    }, 2000);
  };
  const contextPrompt = `You are a competition analysis expert. Your task is to provide detailed, accurate, and helpful information about competitive analysis methodologies, frameworks, and best practices. Focus on providing actionable insights that can help businesses understand their competitors, identify competitive advantages, and develop effective competitive strategies.`;
  
  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Competition Analysis</h2>
        <p className="body-text mb-4">
          Tools and methodologies to analyze competitors and identify market opportunities.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-red-50 hover:bg-red-100">
            <h3 className="raleway-title-h3 mb-2 text-red-800">Competitor Profiling</h3>
            <p className="body-text">Create detailed profiles of your main competitors.</p>
          </div>
          <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-red-50 hover:bg-red-100">
            <h3 className="raleway-title-h3 mb-2 text-red-800">SWOT Analysis</h3>
            <p className="body-text">Analyze strengths, weaknesses, opportunities, and threats in the market.</p>
          </div>
          <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-red-50 hover:bg-red-100">
            <h3 className="raleway-title-h3 mb-2 text-red-800">Competitive Positioning</h3>
            <p className="body-text">Determine your position in the market relative to competitors.</p>
          </div>
          <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-red-50 hover:bg-red-100">
            <h3 className="raleway-title-h3 mb-2 text-red-800">Market Share Analysis</h3>
            <p className="body-text">Analyze market share distribution and identify growth opportunities.</p>
          </div>
        </div>
      </div>
      
      {/* Questionnaire Section with Toggle */}
      <div className="text-center mb-8 p-4 bg-blue-50 rounded-lg border border-blue-100 shadow-md" style={{ borderWidth: '0.5px' }}>
        <h3 className="raleway-title-h3 mb-2">Competition Analysis Questionnaire</h3>
        <p className="body-text mb-4">Complete our comprehensive questionnaire to receive a customized competition analysis strategy for your business.</p>
        <button
          className="mb-4 px-6 py-2 bg-white hover:bg-gray-50 text-gray-800 rounded-lg border border-red-200 shadow-sm transition-colors uppercase"
          onClick={() => setShowQuestionnaire(!showQuestionnaire)}
        >
          {showQuestionnaire ? 'HIDE QUESTIONNAIRE' : 'START QUESTIONNAIRE'}
        </button>
        
        {showQuestionnaire && (
          <div className="mt-6 p-4 rounded-lg">
            <p className="body-text mb-4 text-center">
              Please complete this questionnaire to help us develop a comprehensive competition analysis strategy for your business.
            </p>
            <QuestionnaireLoader 
              title="Competition Analysis Questionnaire" 
              description="Please complete this questionnaire to help us understand your competitive landscape."
              specificQuestionnaires={[
                '00-strategy-questionnaire.yaml',
                '03-ideal-customer-profile.yaml',
                'lead-generation_strategy-quetstionnaire-01.yaml'
              ]}
              defaultQuestionnaire="00-strategy-questionnaire.yaml"
              onSubmit={handleSubmit}
              onGenerateStrategy={handleGenerateStrategy}
              showLocalSave={true}
            />
          </div>
        )}
      </div>
      
      {/* Questionnaire Success Message */}
      {submissionResult && (
        <div className="bg-green-50 p-6 rounded-lg shadow-md border border-green-200 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-700">
            <svg className="inline-block w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {submissionResult.message}
          </h2>
          {submissionResult.isStrategy ? (
            <div>
              <p className="mb-4">
                Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                we've generated a customized competitive analysis strategy for your business.
              </p>
              <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                <h3 className="raleway-title-h3 mb-2">Your Competitive Analysis Strategy</h3>
                <p className="body-text mb-2">This strategy is tailored to help you understand and outperform your competitors:</p>
                <ul className="list-disc pl-5 space-y-2 body-text">
                  <li>Conduct a SWOT analysis for each of your top 3 competitors</li>
                  <li>Analyze pricing strategies across your market segment</li>
                  <li>Evaluate competitors' marketing channels and messaging</li>
                  <li>Identify gaps in competitors' product/service offerings</li>
                  <li>Develop a competitive positioning strategy to differentiate your business</li>
                </ul>
              </div>
            </div>
          ) : (
            <p className="mb-4">
              We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
              Our team will analyze your information and provide tailored competition analysis recommendations.
            </p>
          )}
          <button 
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            onClick={() => setSubmissionResult(null)}
          >
            Close
          </button>
        </div>
      )}
      
      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized competitive analysis strategy...</p>
          </div>
        </div>
      )}
      
      {/* Questionnaire section has been merged with the toggle button section above */}
      
      {/* Competition Analysis AI Agent */}
      <AIAgent 
        title="Competition Analysis Assistant" 
        description="Ask questions about competitive analysis frameworks, tools, and strategies."
        contextPrompt={contextPrompt}
      />
    </div>
  );
}

export default CompetitionAnalysis;
