# Authentication Implementation Guide

This document outlines the authentication system implemented in the Market Research SPA using Supabase Authentication.

## Overview

The authentication system provides:
- User registration and login
- Password reset functionality
- Protected routes
- User profile management
- Integration with questionnaire responses

## Components

### Authentication Context (`AuthContext.jsx`)

The authentication context provides global state management for user authentication:

- `useAuth()` hook for accessing authentication state and functions
- User session management and persistence
- Authentication functions:
  - `signIn(email, password)` - Log in with email and password
  - `signUp(email, password)` - Register a new user
  - `signOut()` - Log out the current user
  - `resetPassword(email)` - Send password reset email
  - `updateProfile(updates)` - Update user profile information

### Authentication UI Components

- **Login.jsx**: Email/password login form
- **Signup.jsx**: New user registration form
- **ForgotPassword.jsx**: Request password reset email
- **ResetPassword.jsx**: Set new password with reset token
- **UserProfile.jsx**: View and edit user profile
- **ProtectedRoute.jsx**: Route wrapper to restrict access to authenticated users

## Data Integration

Questionnaire responses are linked to authenticated users:

- Each response includes the user's ID (`user_id`)
- Responses can be filtered by user ID
- Unauthenticated users can still use the app with localStorage fallback

## Usage

### Login Flow

1. User navigates to `/login`
2. Enters email and password
3. On successful login, redirected to home page
4. Failed login shows appropriate error message

### Registration Flow

1. User navigates to `/signup`
2. Enters email and password
3. Receives confirmation email (if configured in Supabase)
4. Can then log in with credentials

### Password Reset Flow

1. User navigates to `/forgot-password`
2. Enters email address
3. Receives password reset email with link
4. Clicks link to navigate to `/reset-password`
5. Sets new password

### Protected Routes

- All main application routes are protected
- Unauthenticated users are redirected to login
- Authentication state is checked before rendering protected content

### User Profile Management

1. User navigates to `/profile`
2. Can update name and password
3. Email cannot be changed (Supabase limitation)

## Implementation Details

### Supabase Configuration

The application uses Supabase for authentication and data storage:

- Authentication is handled via Supabase Auth
- User data is stored in Supabase Auth tables
- Questionnaire responses are linked to users via `user_id` field

### Environment Variables

The application requires the following environment variables:

```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### Security Considerations

- Authentication tokens are managed by Supabase
- Passwords are never stored in the application
- Protected routes ensure data privacy
- Row-level security can be implemented in Supabase for additional data protection

## Testing Authentication

To test the authentication system:

1. Register a new user via `/signup`
2. Log in with the credentials
3. Complete a questionnaire
4. View your responses
5. Log out and log back in
6. Verify your responses are still accessible
7. Test password reset flow

## Troubleshooting

Common issues:

- **Login fails**: Check credentials and Supabase connection
- **Password reset email not received**: Check spam folder and Supabase email settings
- **Cannot access protected routes**: Ensure user is authenticated
- **User data not persisting**: Check Supabase connection and auth state management
