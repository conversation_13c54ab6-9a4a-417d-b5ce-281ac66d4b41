// Test script for Gemini API
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// Get API key from environment variables
const apiKey = process.env.GEMINI_API_KEY || process.env.google_api_key || 'AIzaSyDxukBSjF4sJkULasg_YLzO0YAE-hUdWlw';
console.log('Using API key:', apiKey ? 'Key is available' : 'No key available');

// Initialize the Gemini API
const genAI = new GoogleGenerativeAI(apiKey);

async function testGemini() {
  try {
    console.log('Initializing Gemini model...');
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
    
    console.log('Generating content...');
    const question = 'What are some effective client acquisition strategies?';
    const result = await model.generateContent(
      `You are a helpful AI assistant specializing in marketing and client acquisition strategies. ` +
      `Please provide a helpful response to the following question: ${question}`
    );
    
    console.log('Raw result:', JSON.stringify(result, null, 2));
    
    // Extract the text from the response
    let responseText;
    
    if (result.response && typeof result.response.text === 'function') {
      responseText = result.response.text();
      console.log('Response text from function call');
    } else if (result.response && result.response.text) {
      responseText = result.response.text;
      console.log('Response text from property');
    } else if (result.text) {
      responseText = result.text;
      console.log('Response text from result.text');
    } else if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
      responseText = result.candidates[0].content.parts[0].text;
      console.log('Response text from candidates');
    } else {
      responseText = 'I apologize, but I could not generate a response at this time.';
      console.log('No response text found');
    }
    
    console.log('\nResponse:', responseText);
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Gemini API:', error);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
testGemini();
