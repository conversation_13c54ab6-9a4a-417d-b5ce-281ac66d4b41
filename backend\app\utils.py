import re
from typing import List
from .models import Questionnaire, Question


def parse_markdown_to_questionnaire(md_text: str, title: str = "Questionnaire") -> Questionnaire:
    """
    Parse a Markdown file to extract questionnaire sections and questions.

    Args:
        md_text (str): The Markdown file content.
        title (str): Title for the questionnaire.

    Returns:
        Questionnaire: Parsed questionnaire object.
    """
    # Example: Look for sections like '## Questionnaire Considerations' and list items
    question_blocks = re.findall(r'##+\s*Questionnaire Considerations[\s\S]*?(?=^## |\Z)', md_text, re.MULTILINE)
    questions: List[Question] = []
    for block in question_blocks:
        # Find markdown list items (e.g., '- What is your age?')
        for match in re.findall(r'^[-*+]\s+(.*)', block, re.MULTILINE):
            questions.append(Question(text=match.strip()))
    # Fallback: If no block found, try to extract all list items as questions
    if not questions:
        for match in re.findall(r'^[-*+]\s+(.*)', md_text, re.MULTILINE):
            questions.append(Question(text=match.strip()))
    return Questionnaire(title=title, questions=questions) 