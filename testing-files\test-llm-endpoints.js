// Test script for LLM API endpoints
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Normalize environment variables
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.openai_api_key;
process.env.DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;

// Log available API keys
console.log('Available API keys:');
console.log('- GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Available' : 'Not available');
console.log('- OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Available' : 'Not available');
console.log('- DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? 'Available' : 'Not available');

// Base URL for API endpoints
const BASE_URL = 'http://localhost:3000';

// Test data for API calls
const testData = {
  prompt: 'Generate a brief marketing tagline for a new eco-friendly water bottle.',
  model: 'gemini-1.5-pro' // Default model for testing
};

// Function to test an API endpoint
async function testEndpoint(endpoint, model) {
  console.log(`\nTesting ${endpoint} API with model ${model}...`);
  
  try {
    console.log(`Making request to: ${BASE_URL}${endpoint}`);
    console.log('Request payload:', { prompt: testData.prompt, model: model });
    
    const response = await axios.post(`${BASE_URL}${endpoint}`, {
      prompt: testData.prompt,
      model: model
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });
    
    console.log(`✅ ${endpoint} API test successful!`);
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2).substring(0, 200) + '...');
    return true;
  } catch (error) {
    console.log(`❌ ${endpoint} API test failed!`);
    
    if (error.response) {
      // The server responded with a status code outside the 2xx range
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.log('No response received. Is the server running?');
      console.log('Error details:', error.message);
    } else {
      // Something happened in setting up the request
      console.log('Error message:', error.message);
    }
    
    return false;
  }
}

// Main function to run all tests
async function runTests() {
  console.log('Starting API endpoint tests...');
  
  // Test Gemini API
  await testEndpoint('/api/gemini', 'gemini-1.5-pro');
  
  // Test OpenAI API
  await testEndpoint('/api/openai', 'gpt-3.5-turbo');
  
  // Test DeepSeek API
  await testEndpoint('/api/deepseek', 'deepseek-chat');
  
  console.log('\nAll tests completed!');
}

// Run the tests
runTests();
