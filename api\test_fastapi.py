import requests
import json
import time
import sys

# Base URL for the FastAPI server
BASE_URL = "http://localhost:3001"

# API endpoints to test
endpoints = [
    {
        "name": "Root",
        "method": "GET",
        "url": f"{BASE_URL}/",
        "data": None
    },
    {
        "name": "<PERSON>",
        "method": "POST",
        "url": f"{BASE_URL}/api/gemini",
        "data": {"prompt": "Generate a short marketing tagline for an eco-friendly water bottle.", "model": "gemini-1.5-pro"}
    },
    {
        "name": "OpenAI",
        "method": "POST",
        "url": f"{BASE_URL}/api/openai",
        "data": {"question": "Generate a short marketing tagline for an eco-friendly water bottle.", "model": "gpt-3.5-turbo"}
    },
    {
        "name": "DeepSeek",
        "method": "POST",
        "url": f"{BASE_URL}/api/deepseek",
        "data": {"prompt": "Generate a short marketing tagline for an eco-friendly water bottle.", "model": "deepseek-chat"}
    }
]

def check_server_status():
    """Check if the FastAPI server is running"""
    try:
        print("Checking if FastAPI server is running...")
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI server is running!")
            return True
        else:
            print(f"❌ FastAPI server returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ FastAPI server is not responding. Make sure it's running with 'python main.py'")
        return False
    except Exception as e:
        print(f"❌ Error checking server status: {str(e)}")
        return False

def test_endpoint(endpoint):
    """Test a specific endpoint"""
    print(f"\n--- Testing {endpoint['name']} API ---")
    
    try:
        if endpoint["method"] == "GET":
            response = requests.get(endpoint["url"], timeout=10)
        else:  # POST
            response = requests.post(endpoint["url"], json=endpoint["data"], timeout=10)
        
        print(f"✅ {endpoint['name']} API responded with status code: {response.status_code}")
        
        if response.status_code == 200:
            print("Response data:")
            print(json.dumps(response.json(), indent=2))
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print("Response:", response.text)
            return False
    except Exception as e:
        print(f"❌ Error testing {endpoint['name']} API: {str(e)}")
        return False

def run_tests():
    """Run all tests"""
    print("Starting FastAPI tests...")
    
    # First check if the server is up
    if not check_server_status():
        print("Cannot proceed with tests as the FastAPI server is not responding.")
        sys.exit(1)
    
    # Wait a bit to ensure the server is fully initialized
    print("Waiting 1 second for server to fully initialize...")
    time.sleep(1)
    
    # Test each endpoint
    success_count = 0
    for endpoint in endpoints:
        if test_endpoint(endpoint):
            success_count += 1
        
        # Wait a bit between requests
        time.sleep(0.5)
    
    # Print summary
    print(f"\nTest Summary: {success_count}/{len(endpoints)} endpoints tested successfully")
    
    if success_count == len(endpoints):
        print("✅ All tests passed!")
    else:
        print(f"❌ {len(endpoints) - success_count} tests failed")

if __name__ == "__main__":
    # Wait a bit before starting tests to ensure server is ready
    print("Waiting 2 seconds before starting tests to ensure server is ready...")
    time.sleep(2)
    run_tests()
