# FastAPI Implementation for Market Research Tool

This directory contains a FastAPI implementation that replaces the previous serverless functions used in the Vercel deployment.

## Features

- Modern Python FastAPI framework with automatic OpenAPI documentation
- Complete API parity with the previous serverless implementation
- Support for all existing endpoints:
  - Questionnaire management
  - Response handling
  - AI model integration (Gemini, OpenAI, DeepSeek, Anthropic, Groq)
- Improved error handling and response validation
- Easy local development and deployment

## Getting Started

### Local Development

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Run the FastAPI server:

```bash
cd api
python main.py
```

The server will start on http://localhost:3001 with auto-reload enabled.

### API Documentation

FastAPI automatically generates interactive API documentation. Once the server is running, you can access:

- Swagger UI: http://localhost:3001/docs
- ReDoc: http://localhost:3001/redoc

### Deployment to Vercel

This implementation includes a `vercel.json` configuration file for easy deployment to Vercel:

1. Install the Vercel CLI:

```bash
npm install -g vercel
```

2. Deploy to Vercel:

```bash
cd api
vercel
```

## API Endpoints

The API provides the following endpoints:

### Root
- `GET /` - Welcome page with endpoint information

### YAML Files
- `GET /api/yaml` - List all YAML files

### Questionnaires
- `GET /api/questionnaire/list` - List all questionnaires
- `GET /api/questionnaire/{file_id}` - Get a specific questionnaire
- `GET /api/questionnaire/generate/{file_id}` - Generate a questionnaire from YAML
- `POST /api/questionnaire/save` - Save a questionnaire

### Responses
- `GET /api/responses/list` - List all responses
- `GET /api/responses/{file_id}` - Get a specific response
- `GET /api/responses/download/{file_id}` - Download a response
- `POST /api/responses/save` - Save a response

### AI Models
- `POST /api/gemini` - Gemini API endpoint
- `POST /api/openai` - OpenAI API endpoint
- `POST /api/deepseek` - DeepSeek API endpoint
- `POST /api/anthropic` - Anthropic API endpoint
- `POST /api/groq` - Groq API endpoint

## Environment Variables

The application uses environment variables for API keys. Make sure to set these in your Vercel deployment or in a `.env` file for local development:

- `GEMINI_API_KEY` - API key for Google's Gemini
- `OPENAI_API_KEY` - API key for OpenAI
- `DEEPSEEK_API_KEY` - API key for DeepSeek
- `ANTHROPIC_API_KEY` - API key for Anthropic
- `GROQ_API_KEY` - API key for Groq

## Differences from Previous Implementation

- Uses FastAPI instead of Express.js
- Proper request/response validation with Pydantic models
- Automatic API documentation
- More consistent error handling
- Better type hints and code organization
