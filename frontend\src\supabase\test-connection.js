import { supabase } from './client.js';

// Test connection to Supabase
async function testConnection() {
  try {
    console.log('Testing connection to Supabase...');
    
    // Test simple query to verify connection
    const { data, error } = await supabase
      .from('questionnaire_responses')
      .select('count(*)', { count: 'exact' });
    
    if (error) {
      console.error('Error connecting to Supabase:', error);
      return false;
    }
    
    console.log('Successfully connected to Supabase!');
    console.log('Current number of questionnaire responses:', data[0].count);
    return true;
  } catch (err) {
    console.error('Exception when connecting to Supabase:', err);
    return false;
  }
}

// Run the test
testConnection().then(success => {
  if (success) {
    console.log('✅ Supabase connection test passed!');
  } else {
    console.error('❌ Supabase connection test failed!');
  }
});

export default testConnection;
