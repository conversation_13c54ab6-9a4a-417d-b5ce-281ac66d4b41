from http.server import BaseHTTPRequestHandler
import json
import os
import sys
import re
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils import get_responses_dir, get_questionnaires_dir

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Extract path and query parameters
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        # Get action and id from query parameters
        action = query_params.get('action', [None])[0]
        file_id = query_params.get('id', [None])[0]
        
        try:
            # Handle different endpoints based on action parameter
            if action == 'list' or parsed_url.path == '/list':
                # List all responses
                self._handle_list()
            elif action == 'download' or '/download/' in parsed_url.path:
                # Download a response
                if file_id is None and '/download/' in parsed_url.path:
                    # Extract file_id from path if not in query params
                    path_parts = parsed_url.path.split('/')
                    file_id = path_parts[-1] if len(path_parts) > 2 else None
                
                if file_id:
                    self._handle_download(file_id)
                else:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Missing file_id parameter"}).encode())
            elif file_id or (parsed_url.path != '/' and parsed_url.path != ''):
                # Get a specific response by ID
                if file_id is None:
                    # Extract file_id from path if not in query params
                    path_parts = parsed_url.path.split('/')
                    file_id = path_parts[-1] if len(path_parts) > 1 else None
                
                if file_id:
                    self._handle_get_by_id(file_id)
                else:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Missing file_id parameter"}).encode())
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Not found"}).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def do_POST(self):
        # Extract path and query parameters
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        # Get action from query parameters
        action = query_params.get('action', [None])[0]
        
        try:
            # Handle different endpoints based on action parameter
            if action == 'save' or parsed_url.path == '/save':
                # Save a response
                self._handle_save()
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Not found"}).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def _handle_list(self):
        """Handle listing all responses"""
        responses_dir = get_responses_dir()
        files = []
        
        # Get files from the responses directory
        if os.path.exists(responses_dir):
            files = [f for f in os.listdir(responses_dir) if f.endswith('.json')]
        
        # Get metadata for each response
        responses = []
        for file in files:
            try:
                file_path = os.path.join(responses_dir, file)
                with open(file_path, 'r') as f:
                    response_data = json.load(f)
                
                # Extract metadata
                metadata = {
                    "id": file.replace('.json', ''),
                    "filename": file,
                    "title": response_data.get("questionnaire_title", "Unknown"),
                    "timestamp": response_data.get("timestamp", ""),
                    "respondent": response_data.get("respondent", {}).get("name", "Anonymous")
                }
                responses.append(metadata)
            except Exception as e:
                print(f"Error reading response file {file}: {str(e)}")
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps({"responses": responses}).encode())

    def _handle_get_by_id(self, file_id):
        """Handle getting a specific response by ID"""
        responses_dir = get_responses_dir()
        file_path = os.path.join(responses_dir, f"{file_id}.json")
        
        if not os.path.exists(file_path):
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Response not found"}).encode())
            return
        
        with open(file_path, 'r') as f:
            response = json.load(f)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def _handle_download(self, file_id):
        """Handle downloading a response"""
        responses_dir = get_responses_dir()
        file_path = os.path.join(responses_dir, f"{file_id}.json")
        
        if not os.path.exists(file_path):
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Response not found"}).encode())
            return
        
        with open(file_path, 'r') as f:
            response = json.load(f)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Content-Disposition', f'attachment; filename="{file_id}.json"')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(response, indent=2).encode())

    def _handle_save(self):
        """Handle saving a response"""
        responses_dir = get_responses_dir()
        
        # Read request body
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        response_data = json.loads(post_data.decode())
        
        # Add timestamp if not present
        if 'timestamp' not in response_data:
            response_data['timestamp'] = datetime.now().isoformat()
        
        # Generate a filename if not provided
        if 'filename' not in response_data:
            questionnaire_title = response_data.get('questionnaire_title', 'response')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{re.sub(r'[^\w\-_]', '_', questionnaire_title.lower())}_{timestamp}.json"
        else:
            filename = response_data['filename']
            del response_data['filename']
        
        # Ensure filename has .json extension
        if not filename.endswith('.json'):
            filename += '.json'
        
        # Save the response
        file_path = os.path.join(responses_dir, filename)
        with open(file_path, 'w') as f:
            json.dump(response_data, f)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps({"success": True, "filename": filename}).encode())
