import os
import httpx
import json
from typing import Dict, Any, Optional
import asyncio

class AnthropicService:
    """Service for handling Anthropic API interactions"""
    
    def __init__(self):
        self.api_key = os.environ.get("ANTHROPIC_API_KEY")
        self.base_url = "https://api.anthropic.com/v1"
        
    async def generate_response(
        self, 
        prompt: str, 
        model: str = "claude-3-opus-20240229", 
        thinking_mode: bool = False
    ) -> Dict[str, Any]:
        """Generate response using Anthropic API"""
        
        if not self.api_key:
            return {
                "text": f"Mock response from Anthropic {model}: {prompt[:100]}...",
                "model": model,
                "success": True,
                "mock": True,
                "error": "No API key configured"
            }
        
        try:
            # Prepare the final prompt based on thinking mode
            final_prompt = self._prepare_prompt(prompt, thinking_mode)
            
            # Prepare the request payload
            payload = {
                "model": model,
                "max_tokens": 2000,
                "messages": [
                    {
                        "role": "user", 
                        "content": final_prompt
                    }
                ],
                "system": "You are a helpful marketing and client acquisition expert. Provide detailed, actionable advice with examples when possible."
            }
            
            # Make the API call
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/messages",
                    headers={
                        "x-api-key": self.api_key,
                        "Content-Type": "application/json",
                        "anthropic-version": "2023-06-01"
                    },
                    json=payload
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    return {
                        "text": f"Error calling Anthropic API: {error_detail}",
                        "model": model,
                        "success": False,
                        "error": error_detail
                    }
                
                result = response.json()
                
                # Extract the response text
                if "content" in result and len(result["content"]) > 0:
                    content = result["content"][0]
                    if "text" in content:
                        response_text = content["text"]
                        
                        return {
                            "text": response_text,
                            "model": model,
                            "success": True,
                            "thinkingMode": thinking_mode
                        }
                
                # If we can't extract the response, return an error
                return {
                    "text": "No valid response from Anthropic API",
                    "model": model,
                    "success": False,
                    "error": "Invalid response format"
                }
                
        except httpx.TimeoutException:
            return {
                "text": "Request to Anthropic API timed out",
                "model": model,
                "success": False,
                "error": "Timeout"
            }
        except Exception as e:
            return {
                "text": f"Error calling Anthropic API: {str(e)}",
                "model": model,
                "success": False,
                "error": str(e)
            }
    
    def _prepare_prompt(self, prompt: str, thinking_mode: bool) -> str:
        """Prepare the prompt based on thinking mode"""
        
        if thinking_mode:
            return f"""When answering, please follow this structure:
1. THINKING: First, think step by step about the question. Consider different angles, relevant marketing concepts, and potential strategies. This section is for your analytical process.
2. ANSWER: Then provide your final, well-structured answer based on your thinking.

Question: {prompt}"""
        else:
            return prompt
