const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m'
};

console.log(`${colors.blue}Starting build process for Market Research Tool...${colors.reset}`);

// Ensure the dist directory exists and is empty
console.log(`${colors.yellow}Cleaning output directory...${colors.reset}`);
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
  fs.rmSync(distDir, { recursive: true, force: true });
}
fs.mkdirSync(distDir, { recursive: true });

try {
  // Build the frontend
  console.log(`${colors.yellow}Building frontend application...${colors.reset}`);
  execSync('cd frontend && npm run build', { stdio: 'inherit' });
  
  // Copy the frontend build to the dist directory
  console.log(`${colors.yellow}Copying frontend build to dist directory...${colors.reset}`);
  fs.cpSync(path.join(__dirname, 'frontend', 'dist'), distDir, { recursive: true });
  
  // Copy public directory to dist for static assets
  console.log(`${colors.yellow}Copying public assets to dist directory...${colors.reset}`);
  const publicDir = path.join(__dirname, 'frontend', 'public');
  const distPublicDir = path.join(distDir, 'public');
  if (fs.existsSync(publicDir)) {
    if (!fs.existsSync(distPublicDir)) {
      fs.mkdirSync(distPublicDir, { recursive: true });
    }
    fs.cpSync(publicDir, distPublicDir, { recursive: true });
  }
  
  // Create data directories for serverless functions
  console.log(`${colors.yellow}Creating data directories for serverless functions...${colors.reset}`);
  const dataDir = path.join(__dirname, 'data');
  const questionnairesDir = path.join(dataDir, 'questionnaires');
  const responsesDir = path.join(dataDir, 'responses');
  
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  if (!fs.existsSync(questionnairesDir)) {
    fs.mkdirSync(questionnairesDir, { recursive: true });
  }
  if (!fs.existsSync(responsesDir)) {
    fs.mkdirSync(responsesDir, { recursive: true });
  }
  
  console.log(`${colors.green}Build completed successfully!${colors.reset}`);
  console.log(`${colors.blue}You can now deploy to Vercel using:${colors.reset}`);
  console.log(`${colors.yellow}vercel${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Build failed:${colors.reset}`, error);
  process.exit(1);
}
