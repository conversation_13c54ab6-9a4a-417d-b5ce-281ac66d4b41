import os
import httpx
import json
from typing import Dict, Any, Optional
import asyncio

class GeminiService:
    """Service for handling Gemini API interactions"""
    
    def __init__(self):
        self.api_key = os.environ.get("GEMINI_API_KEY")
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        
    async def generate_response(
        self, 
        prompt: str, 
        model: str = "gemini-1.5-pro", 
        thinking_mode: bool = False
    ) -> Dict[str, Any]:
        """Generate response using Gemini API"""
        
        if not self.api_key:
            return {
                "text": f"Mock response from Gemini {model}: {prompt[:100]}...",
                "model": model,
                "success": True,
                "mock": True,
                "error": "No API key configured"
            }
        
        try:
            # Prepare the final prompt based on thinking mode
            final_prompt = self._prepare_prompt(prompt, thinking_mode)
            
            # Prepare the request payload
            payload = {
                "contents": [{
                    "parts": [{
                        "text": final_prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 2000,
                    "topP": 0.8,
                    "topK": 40
                }
            }
            
            # Make the API call
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/{model}:generateContent",
                    params={"key": self.api_key},
                    headers={"Content-Type": "application/json"},
                    json=payload
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    return {
                        "text": f"Error calling Gemini API: {error_detail}",
                        "model": model,
                        "success": False,
                        "error": error_detail
                    }
                
                result = response.json()
                
                # Extract the response text
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]
                    if "content" in candidate and "parts" in candidate["content"]:
                        response_text = candidate["content"]["parts"][0]["text"]
                        
                        return {
                            "text": response_text,
                            "answer": response_text,  # For compatibility with frontend
                            "model": model,
                            "success": True,
                            "thinkingMode": thinking_mode
                        }
                
                # If we can't extract the response, return an error
                return {
                    "text": "No valid response from Gemini API",
                    "model": model,
                    "success": False,
                    "error": "Invalid response format"
                }
                
        except httpx.TimeoutException:
            return {
                "text": "Request to Gemini API timed out",
                "model": model,
                "success": False,
                "error": "Timeout"
            }
        except Exception as e:
            return {
                "text": f"Error calling Gemini API: {str(e)}",
                "model": model,
                "success": False,
                "error": str(e)
            }
    
    def _prepare_prompt(self, prompt: str, thinking_mode: bool) -> str:
        """Prepare the prompt based on thinking mode"""
        
        if thinking_mode:
            return f"""You are a helpful AI assistant specializing in marketing and client acquisition strategies.
When answering, please follow this structure:
1. THINKING: First, think step by step about the question. Consider different angles, relevant marketing concepts, and potential strategies. This section is for your analytical process.
2. ANSWER: Then provide your final, well-structured answer based on your thinking.

Question: {prompt}"""
        else:
            # If the prompt already includes system instructions, use it directly
            if prompt.startswith("You are") or len(prompt) > 500:
                return prompt
            else:
                return f"""You are a helpful AI assistant specializing in marketing and client acquisition strategies.
Please provide a helpful response to the following question: {prompt}"""
