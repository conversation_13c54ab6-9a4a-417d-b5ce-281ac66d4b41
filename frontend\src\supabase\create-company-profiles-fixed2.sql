-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- First create the trigger function outside the main function
CREATE OR REPLACE FUNCTION update_company_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- SQL function to create the company_profiles table
CREATE OR REPLACE FUNCTION create_company_profiles_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  policy_exists BOOLEAN;
BEGIN
  -- Create the company_profiles table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.company_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT,
    industry TEXT,
    company_size TEXT,
    founded_year INTEGER,
    location TEXT,
    mission TEXT,
    vision TEXT,
    core_values TEXT,
    target_audience TEXT,
    unique_selling_points TEXT,
    competitors TEXT,
    challenges TEXT,
    business_goals TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id)
  );

  -- Add comment to the table
  COMMENT ON TABLE public.company_profiles IS 'Stores company profile information for users';

  -- Enable RLS
  ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;

  -- Check if select policy exists
  SELECT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'company_profiles' 
    AND policyname = 'select_own_company_profile'
  ) INTO policy_exists;
  
  -- Create select policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY select_own_company_profile ON public.company_profiles
      FOR SELECT USING (auth.uid() = user_id);
  END IF;

  -- Check if insert policy exists
  SELECT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'company_profiles' 
    AND policyname = 'insert_own_company_profile'
  ) INTO policy_exists;
  
  -- Create insert policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY insert_own_company_profile ON public.company_profiles
      FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  -- Check if update policy exists
  SELECT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'company_profiles' 
    AND policyname = 'update_own_company_profile'
  ) INTO policy_exists;
  
  -- Create update policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY update_own_company_profile ON public.company_profiles
      FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  -- Check if delete policy exists
  SELECT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'company_profiles' 
    AND policyname = 'delete_own_company_profile'
  ) INTO policy_exists;
  
  -- Create delete policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY delete_own_company_profile ON public.company_profiles
      FOR DELETE USING (auth.uid() = user_id);
  END IF;

  -- Create trigger for the updated_at column
  DROP TRIGGER IF EXISTS update_company_profiles_updated_at ON public.company_profiles;
  
  CREATE TRIGGER update_company_profiles_updated_at
  BEFORE UPDATE ON public.company_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_company_profiles_updated_at();

END;
$$;

-- Execute the function to create the table
SELECT create_company_profiles_table();

-- Grant access to the table
GRANT ALL ON public.company_profiles TO authenticated;
GRANT ALL ON public.company_profiles TO service_role;
