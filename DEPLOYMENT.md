# Deploying to Vercel

This guide walks you through the process of deploying the Market Research Tool to Vercel.

## Prerequisites

1. A [Vercel account](https://vercel.com/signup)
2. [Git](https://git-scm.com/downloads) installed on your machine
3. [Node.js](https://nodejs.org/) (version 14 or higher)
4. [Vercel CLI](https://vercel.com/docs/cli) (optional, but recommended)

## Deployment Steps

### 1. Prepare Your Project

Make sure your project is ready for deployment:

```bash
# Install dependencies
npm install

# Build the project
npm run build
```

### 2. Push to GitHub

If your project isn't already on GitHub:

```bash
# Initialize Git repository (if not already done)
git init

# Add all files
git add .

# Commit changes
git commit -m "Prepare for Vercel deployment"

# Add your GitHub repository as remote
git remote add origin https://github.com/yourusername/your-repo.git

# Push to GitHub
git push -u origin master
```

### 3. Deploy with Vercel CLI (Option 1)

The easiest way to deploy is using the Vercel CLI:

```bash
# Install Vercel CLI globally (if not already installed)
npm install -g vercel

# Deploy to Vercel
vercel
```

Follow the prompts to link your project to your Vercel account.

### 4. Deploy from Vercel Dashboard (Option 2)

Alternatively, you can deploy directly from the Vercel dashboard:

1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Other
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`
5. Click "Deploy"

## Environment Variables

No environment variables are required for basic functionality. However, if you need to connect to external services, you can add them in the Vercel dashboard under "Settings" > "Environment Variables".

## Custom Domain

To use a custom domain:

1. Go to your project in the Vercel dashboard
2. Click "Settings" > "Domains"
3. Add your domain and follow the instructions to configure DNS

## Troubleshooting

If you encounter issues during deployment:

1. Check the Vercel deployment logs for errors
2. Ensure all dependencies are correctly listed in package.json
3. Verify that the build script is working correctly locally
4. Check that the vercel.json configuration is correct

For more help, refer to the [Vercel documentation](https://vercel.com/docs) or open an issue in the project repository.
