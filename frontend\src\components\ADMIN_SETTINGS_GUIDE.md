# Admin Settings Page Guide

The Admin Settings page provides a centralized interface for managing all administrative aspects of your market research tool.

## Access

### Who Can Access
- Users with email containing "admin" (e.g., <EMAIL>)
- Users with email "<EMAIL>"
- Users with email ending in your domain (configurable in AdminSettings.jsx)

### How to Access
1. **Main Navigation**: Admin users see an "Admin" tab in the main navigation menu
2. **Profile Dropdown**: Admin users see "Admin Settings" in their profile dropdown
3. **Direct URL**: Navigate to `/admin` (requires admin privileges)

## Features Overview

### 1. Overview Tab
**Purpose**: Dashboard with system statistics and quick actions

**Features**:
- System statistics (users, responses, active clients)
- System health indicators
- Recent activity feed
- Quick action buttons to other admin sections

**Key Metrics**:
- Total Users: Number of registered users
- Total Responses: Number of questionnaire responses
- Active Clients: Number of clients with recent activity
- System Status: Overall health indicator

### 2. Model Configuration Tab
**Purpose**: Manage AI model access for different clients

**Features**:
- Complete model configuration interface (embedded ModelConfigAdmin)
- Add/remove clients
- Configure model availability per client
- Preview client access
- Export configuration

**Use Cases**:
- Set up new client with limited model access
- Create client tiers (free, basic, premium)
- Temporarily restrict access for quota management
- Demo account configuration

### 3. User Management Tab
**Purpose**: Manage user accounts and permissions

**Current Features**:
- View current admin user information
- Quick actions for user management
- Links to external user management tools

**Planned Features**:
- View all registered users
- Manage user permissions and roles
- Set usage quotas and limits
- Bulk user operations
- User activity monitoring

### 4. System Settings Tab
**Purpose**: Configure global application settings

**Current Features**:
- View current system configuration
- System health monitoring
- Quick maintenance actions
- Configuration export

**Planned Features**:
- Dynamic API endpoint configuration
- Global default model settings
- Rate limiting configuration
- Automated backup settings
- Email notification settings
- Performance monitoring

## Usage Examples

### Setting Up a New Client
1. Go to Admin Settings → Model Configuration
2. Click "Add Client"
3. Enter client email (e.g., "<EMAIL>")
4. Configure their model access:
   - Disable expensive models for basic tier
   - Enable all models for premium tier
5. Use preview feature to verify configuration

### Managing System Health
1. Go to Admin Settings → System Settings
2. Check system health indicators
3. Use quick actions for maintenance:
   - Clear cache if needed
   - Test API connections
   - Export logs for debugging

### Monitoring Usage
1. Go to Admin Settings → Overview
2. Review system statistics
3. Check recent activity
4. Identify trends and usage patterns

## Configuration

### Admin Access Control
Edit `AdminSettings.jsx` to modify who has admin access:

```javascript
const isAdmin = user && (
  user.email === '<EMAIL>' || 
  user.email?.includes('admin') ||
  user.email?.endsWith('@yourdomain.com') // Add your domain
);
```

### Adding New Admin Features
1. Add new tab to the `tabs` array
2. Create render function for the new tab
3. Add tab content to the main render method

Example:
```javascript
{
  id: 'analytics',
  name: 'Analytics',
  icon: (/* SVG icon */)
}
```

### Customizing Statistics
Modify the `loadSystemStats` function to fetch real data:

```javascript
const loadSystemStats = async () => {
  try {
    const users = await fetch('/api/admin/users/count');
    const responses = await fetch('/api/admin/responses/count');
    // Update state with real data
  } catch (error) {
    console.error('Failed to load stats:', error);
  }
};
```

## Security Considerations

### Access Control
- Admin access is checked on both frontend and should be verified on backend
- Sensitive operations should require additional authentication
- Admin actions should be logged for audit purposes

### Data Protection
- Admin interface may display sensitive user information
- Ensure proper data handling and privacy compliance
- Implement proper session management for admin users

### API Security
- Admin API endpoints should have additional security measures
- Rate limiting for admin operations
- Input validation for all admin actions

## Troubleshooting

### Admin Access Issues
**Problem**: User can't access admin settings
**Solutions**:
1. Check if user email meets admin criteria
2. Verify user is properly logged in
3. Check browser console for errors
4. Ensure admin routes are properly configured

### Missing Features
**Problem**: Expected admin features not showing
**Solutions**:
1. Check if features are implemented or planned
2. Verify tab configuration in AdminSettings.jsx
3. Check for JavaScript errors in console

### Performance Issues
**Problem**: Admin page loads slowly
**Solutions**:
1. Check if statistics loading is taking too long
2. Implement caching for frequently accessed data
3. Add loading indicators for better UX

## Future Enhancements

### Planned Features
1. **Real-time Dashboard**: Live updates of system metrics
2. **Advanced User Management**: Complete user CRUD operations
3. **Usage Analytics**: Detailed usage reports and charts
4. **System Monitoring**: Real-time performance metrics
5. **Backup Management**: Automated backup configuration
6. **Email Notifications**: Alert system for admin events
7. **API Management**: Dynamic API configuration
8. **Audit Logs**: Complete audit trail of admin actions

### Integration Opportunities
1. **External Analytics**: Google Analytics, Mixpanel integration
2. **Monitoring Tools**: Integration with monitoring services
3. **Notification Systems**: Slack, email, SMS notifications
4. **Database Management**: Direct database administration tools
5. **File Management**: Cloud storage integration

## Best Practices

### Regular Maintenance
1. **Weekly**: Review system statistics and user activity
2. **Monthly**: Update client configurations as needed
3. **Quarterly**: Review and update admin access permissions
4. **As Needed**: Monitor system health and performance

### Documentation
1. Keep admin procedures documented
2. Maintain change logs for configuration updates
3. Document any custom modifications
4. Train other admin users on procedures

### Monitoring
1. Set up alerts for system issues
2. Monitor user feedback for admin-related problems
3. Track admin action effectiveness
4. Regular security audits

The Admin Settings page is designed to be extensible and can be enhanced with additional features as your needs grow.
