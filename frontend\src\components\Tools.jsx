import React, { useState } from 'react';
import QuestionnaireBuilder from './QuestionnaireBuilder';
import ResponseViewer from './ResponseViewer';

function Tools() {
  const [activeTab, setActiveTab] = useState('questionnaire');

  return (
    <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center">
      <h2 className="raleway-title-h1 mb-4">Research Tools</h2>
      <p className="body-text mb-4">
        Create, fill out, and analyze marketing research questionnaires.
      </p>
      
      {/* Navigation Tabs */}
      <div className="flex border-b mb-6">
        <button
          className={`py-2 px-4 mr-4 raleway-menu ${activeTab === 'questionnaire' 
            ? 'border-b-2 border-blue-500 text-blue-600' 
            : 'text-gray-500 hover:text-blue-400'}`}
          onClick={() => setActiveTab('questionnaire')}
        >
          Fill Questionnaire
        </button>
        <button
          className={`py-2 px-4 mr-4 raleway-menu ${activeTab === 'responses' 
            ? 'border-b-2 border-blue-500 text-blue-600' 
            : 'text-gray-500 hover:text-blue-400'}`}
          onClick={() => setActiveTab('responses')}
        >
          View Responses
        </button>
      </div>
      
      {/* Content based on active tab */}
      {activeTab === 'questionnaire' ? <QuestionnaireBuilder /> : <ResponseViewer />}
    </div>
  );
}

export default Tools;
