# Railway Backend Deployment Guide

This guide will help you deploy the clean Railway backend for your Market Research Tool.

## Prerequisites

1. Railway account (sign up at https://railway.app)
2. Your LLM API keys (OpenAI, Gemini, etc.)
3. Git repository access

## Step 1: Create Railway Project

1. Go to https://railway.app and sign in
2. Click "New Project"
3. Choose "Deploy from GitHub repo"
4. Select your repository
5. Choose "railway-backend" as the root directory

## Step 2: Configure Environment Variables

In your Railway project dashboard, go to Variables and add:

```
OPENAI_API_KEY=sk-your-openai-key-here
GEMINI_API_KEY=your-gemini-key-here
DEEPSEEK_API_KEY=your-deepseek-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
GROQ_API_KEY=your-groq-key-here
```

**Note**: You don't need to set all API keys. The backend will work with mock responses for any missing keys.

## Step 3: Deploy

1. Railway will automatically detect the Python project
2. It will install dependencies from `requirements.txt`
3. Start the server with the command from `railway.json`
4. Monitor the deployment in the Railway dashboard

## Step 4: Get Your Railway URL

1. Once deployed, Railway will provide a URL like: `https://your-app-name.railway.app`
2. Test the health endpoint: `https://your-app-name.railway.app/health`
3. You should see a JSON response with status "healthy"

## Step 5: Update Frontend Configuration

Update your Vercel frontend to use the Railway backend for LLM operations:

### Option A: Update existing config.js

```javascript
// In frontend/src/config.js
const RAILWAY_API_BASE_URL = 'https://your-app-name.railway.app';

// Update LLM endpoints to use Railway
export default {
  // Keep existing Vercel API for questionnaires/responses
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://your-vercel-app.vercel.app/api'
    : 'http://localhost:3001/api',
    
  // Add Railway API for LLM operations
  LLM_API_BASE_URL: RAILWAY_API_BASE_URL,
  
  endpoints: {
    // LLM endpoints (Railway)
    gemini: { ask: `${RAILWAY_API_BASE_URL}/api/gemini` },
    openai: { ask: `${RAILWAY_API_BASE_URL}/api/openai` },
    deepseek: { ask: `${RAILWAY_API_BASE_URL}/api/deepseek` },
    
    // Regular endpoints (Vercel)
    questionnaire: {
      list: `${API_BASE_URL}/questionnaire/list`,
      // ... other endpoints
    }
  }
};
```

### Option B: Environment-based configuration

Add to your Vercel environment variables:
```
VITE_RAILWAY_API_URL=https://your-app-name.railway.app
```

Then in your config:
```javascript
const RAILWAY_API_BASE_URL = import.meta.env.VITE_RAILWAY_API_URL || 'http://localhost:8000';
```

## Step 6: Update Frontend Components

Update your React components to use the Railway endpoints for LLM calls:

```javascript
// In AIAgent.jsx or similar components
const callLLMAPI = async (prompt, model, provider) => {
  const endpoint = `${config.LLM_API_BASE_URL}/api/${provider}`;
  
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: prompt,
      model: model,
      thinkingMode: false
    })
  });
  
  return await response.json();
};
```

## Step 7: Test the Integration

1. Deploy your updated frontend to Vercel
2. Test LLM operations to ensure they're using Railway
3. Test regular operations to ensure they're still using Vercel
4. Check Railway logs for LLM requests
5. Check Vercel logs for regular API requests

## Monitoring and Maintenance

### Railway Dashboard
- Monitor deployment status
- View logs and metrics
- Manage environment variables
- Scale resources if needed

### Health Checks
- Railway automatically monitors `/health` endpoint
- Set up alerts for downtime
- Monitor response times

### Cost Optimization
- Railway charges based on usage
- LLM operations are billed separately by each provider
- Monitor usage in Railway dashboard

## Troubleshooting

### Common Issues

1. **Deployment fails**: Check Railway logs for Python/dependency errors
2. **CORS errors**: Ensure frontend domain is allowed in CORS settings
3. **API key errors**: Verify environment variables are set correctly
4. **Timeout errors**: Check if LLM provider is responding

### Debug Steps

1. Check Railway deployment logs
2. Test health endpoint: `curl https://your-app.railway.app/health`
3. Test LLM endpoint: 
   ```bash
   curl -X POST https://your-app.railway.app/api/gemini \
     -H "Content-Type: application/json" \
     -d '{"prompt": "test", "model": "gemini-1.5-pro"}'
   ```

## Benefits of This Architecture

✅ **Separation of Concerns**: LLM operations on Railway, UI on Vercel
✅ **No Timeouts**: Railway handles long-running LLM requests
✅ **Better Performance**: Dedicated resources for AI operations
✅ **Cost Effective**: Pay only for what you use
✅ **Scalable**: Railway auto-scales based on demand
✅ **Clean Codebase**: Separate backend focused only on LLM operations

## Next Steps

1. Set up monitoring and alerts
2. Add authentication if needed
3. Implement rate limiting
4. Add caching for common requests
5. Set up CI/CD pipeline for automatic deployments
