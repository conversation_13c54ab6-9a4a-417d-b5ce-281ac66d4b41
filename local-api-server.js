const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const geminiHandler = require('./api/gemini.js');
const openaiHandler = require('./api/openai.js');
const deepseekHandler = require('./api/deepseek.js');

// Load environment variables from .env file
dotenv.config();

// Create Express app
const app = express();

// Enable CORS
app.use(cors());

// Parse JSON request bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const PORT = process.env.PORT || 3000;

// API Routes
app.post('/api/gemini', (req, res) => {
  // Call the Gemini serverless function handler
  return geminiHandler(req, res);
});

app.post('/api/openai', (req, res) => {
  // Call the OpenAI serverless function handler
  return openaiHandler(req, res);
});

app.post('/api/deepseek', (req, res) => {
  console.log('DeepSeek API endpoint called');
  console.log('Request body:', req.body);
  
  try {
    // Call the DeepSeek serverless function handler
    return deepseekHandler(req, res);
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({ error: 'Internal server error', details: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Local API server running at http://localhost:${PORT}`);
  console.log(`API endpoints available at:`);
  console.log(`- http://localhost:${PORT}/api/gemini`);
  console.log(`- http://localhost:${PORT}/api/openai`);
  console.log(`- http://localhost:${PORT}/api/deepseek`);
});
