import { supabase, saveQuestionnaireResponses } from './client.js';

// Test saving a questionnaire response
async function testSaveResponse() {
  try {
    console.log('Testing saving questionnaire response to Supabase...');
    
    // Get current user
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Error getting current user:', authError);
      return false;
    }
    
    console.log('Current user:', authData.user ? `ID: ${authData.user.id}, Email: ${authData.user.email}` : 'Not authenticated');
    
    // Only proceed if authenticated
    if (!authData.user) {
      console.error('Not authenticated. Please log in to test saving responses.');
      return false;
    }
    
    // Test data
    const testData = {
      questionnaireType: 'consumerInsight',
      questionnaireName: 'Consumer Insights',
      responses: {
        test_question_1: 'Test answer 1',
        test_question_2: 'Test answer 2'
      }
    };
    
    console.log('Attempting to save test data:', testData);
    
    // Try to save
    const { data, error } = await saveQuestionnaireResponses(
      testData.questionnaireType,
      testData.questionnaireName,
      testData.responses
    );
    
    if (error) {
      console.error('Error saving test response:', error);
      
      // Check if it's a permissions issue
      if (error.code === '42501' || error.message.includes('permission')) {
        console.error('This appears to be a permissions issue. Check your Row Level Security (RLS) policies.');
      }
      
      // Check if it's a foreign key constraint
      if (error.code === '23503') {
        console.error('This appears to be a foreign key constraint issue.');
      }
      
      // Check if it's a table doesn't exist issue
      if (error.code === '42P01') {
        console.error('The table "questionnaire_responses" might not exist.');
      }
      
      return false;
    }
    
    console.log('Successfully saved test response!', data);
    
    // Verify by fetching the saved data
    const { data: verifyData, error: verifyError } = await supabase
      .from('questionnaire_responses')
      .select('*')
      .eq('questionnaire_type', testData.questionnaireType)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying saved data:', verifyError);
      return false;
    }
    
    console.log('Verification data:', verifyData);
    return true;
  } catch (err) {
    console.error('Exception when testing save response:', err);
    return false;
  }
}

// IMPORTANT: This test function should only be run manually when needed
// DO NOT automatically run this as it will add test records to the database
//
// To run the test manually, call:
// testSaveResponse().then(success => {
//   if (success) {
//     console.log('✅ Supabase save test passed!');
//   } else {
//     console.error('❌ Supabase save test failed!');
//   }
// });

export default testSaveResponse;
