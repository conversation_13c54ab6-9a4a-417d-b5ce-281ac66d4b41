// Vercel serverless function for saving documents
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const mkdirp = require('mkdirp');

const writeFileAsync = promisify(fs.writeFile);

module.exports = async (req, res) => {
  console.log('Save document serverless function called');
  console.log('Request method:', req.method);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    res.status(204).end();
    return;
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    console.log('Request body:', JSON.stringify(req.body));
    
    const { documentName, content, question } = req.body;
    
    if (!documentName || !content) {
      return res.status(400).json({ error: 'Document name and content are required' });
    }
    
    // Create client-documents directory if it doesn't exist
    const clientDocsDir = path.join(process.cwd(), 'client-documents');
    await mkdirp(clientDocsDir);
    
    // Create a filename-safe version of the document name
    const safeDocName = documentName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${safeDocName}_${timestamp}.md`;
    const filePath = path.join(clientDocsDir, filename);
    
    // Format the content with the question
    const formattedContent = `# ${documentName}\n\n## Question\n${question || 'N/A'}\n\n## Answer\n${content}`;
    
    // Write the file
    await writeFileAsync(filePath, formattedContent, 'utf8');
    console.log(`Document saved to: ${filePath}`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Document saved successfully',
      filename: filename,
      path: filePath
    });
    
  } catch (error) {
    console.error('Error saving document:', error);
    console.error('Error stack:', error.stack);
    
    return res.status(500).json({ 
      error: error.message || 'Failed to save document',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};
