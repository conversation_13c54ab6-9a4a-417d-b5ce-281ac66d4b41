import pytest
from fastapi.testclient import TestClient
from backend.app.main import app
from backend.app.utils import parse_markdown_to_questionnaire

client = TestClient(app)

SAMPLE_MD = """
## Questionnaire Considerations
- What is your age?
- What is your occupation?
"""

EMPTY_MD = """
# No questions here
Just some text.
"""

def test_parse_markdown_to_questionnaire():
    q = parse_markdown_to_questionnaire(SAMPLE_MD, title="Demo")
    assert q.title == "Demo"
    assert len(q.questions) == 2
    assert q.questions[0].text == "What is your age?"

def test_parse_markdown_to_questionnaire_empty():
    q = parse_markdown_to_questionnaire(EMPTY_MD, title="Empty")
    assert q.title == "Empty"
    assert len(q.questions) == 0

def test_generate_questionnaire_endpoint():
    response = client.post("/api/questionnaire/generate", data={"title": "Demo", "filename": "strategy.md"})
    assert response.status_code == 200 or response.status_code == 404  # File may or may not exist

def test_save_and_list_questionnaire():
    q = {"title": "TestQ", "questions": [{"text": "Q1"}]}
    save_resp = client.post("/api/questionnaire/save", json=q)
    assert save_resp.status_code == 200
    list_resp = client.get("/api/questionnaire/list")
    assert list_resp.status_code == 200
    assert "files" in list_resp.json() 