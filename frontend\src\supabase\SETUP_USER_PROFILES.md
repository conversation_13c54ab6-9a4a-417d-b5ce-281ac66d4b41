# User Profiles Setup Guide

This guide will help you set up the user profiles system with proper admin roles for your market research tool.

## Step 1: Create the Database Table

1. **Open Supabase Dashboard**
   - Go to your Supabase project dashboard
   - Navigate to the SQL Editor

2. **Run the SQL Script**
   - Copy the contents of `create-user-profiles-table.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute the script

This will create:
- `user_profiles` table with roles, tiers, and permissions
- Automatic profile creation for new users
- Row Level Security (RLS) policies
- Proper indexes for performance

## Step 2: Create Your First Admin User

### Option A: Make Yourself Admin (Recommended)

1. **Sign up normally** through your app's signup process
2. **Run this SQL** in Supabase SQL Editor (replace with your email):

```sql
UPDATE public.user_profiles 
SET role = 'admin', tier = 'premium' 
WHERE email = '<EMAIL>';
```

### Option B: Insert Admin Directly

If you haven't signed up yet, you can create an admin user directly:

```sql
-- First, sign up through the app, then run this:
INSERT INTO public.user_profiles (user_id, email, full_name, role, tier)
VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  '<EMAIL>',
  'Your Name',
  'admin',
  'premium'
)
ON CONFLICT (email) DO UPDATE SET
  role = 'admin',
  tier = 'premium',
  updated_at = now();
```

## Step 3: Verify the Setup

1. **Log in** to your application with your admin account
2. **Check the navigation** - you should see an "Admin" tab
3. **Access Admin Settings** - click on Admin in the navigation or Profile → Admin Settings
4. **Test Model Configuration** - go to the Model Configuration tab and try adding a client

## Step 4: Configure Client Access

### Using the Admin Interface

1. Go to **Admin Settings → Model Configuration**
2. Click **"Add Client"**
3. Enter the client's email address
4. **Configure their model access**:
   - Uncheck expensive models for basic clients
   - Leave all models checked for premium clients
5. Use the **Preview** feature to see what models they'll have access to

### Using SQL (Advanced)

You can also configure users directly in the database:

```sql
-- Set a user to basic tier (limited models)
UPDATE public.user_profiles 
SET tier = 'basic' 
WHERE email = '<EMAIL>';

-- Set specific model permissions
UPDATE public.user_profiles 
SET permissions = '{
  "models": {
    "o1-preview": false,
    "gpt-4o": false,
    "claude-3-opus-20240229": false
  }
}'
WHERE email = '<EMAIL>';
```

## User Tiers and Model Access

### Free Tier
- Basic models only: GPT-3.5, Gemini Flash, Claude Haiku
- No access to premium reasoning models

### Basic Tier  
- Includes some premium models: GPT-4 Turbo, Claude 3.5 Sonnet
- Limited reasoning models: o1-mini only

### Premium Tier
- Access to all models including o1-preview
- No restrictions

## Troubleshooting

### Admin Access Not Working

**Problem**: Can't see Admin menu or access admin features

**Solutions**:
1. Check your user profile in the database:
```sql
SELECT * FROM public.user_profiles WHERE email = '<EMAIL>';
```

2. Ensure your role is set to 'admin':
```sql
UPDATE public.user_profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

3. Clear browser cache and reload the page

### User Profile Not Created

**Problem**: New users don't get profiles automatically

**Solutions**:
1. Check if the trigger is working:
```sql
SELECT * FROM pg_trigger WHERE tgname = 'on_auth_user_created';
```

2. Manually create profile for existing users:
```sql
INSERT INTO public.user_profiles (user_id, email, role, tier)
SELECT id, email, 'user', 'free' 
FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM public.user_profiles);
```

### Model Configuration Not Working

**Problem**: Users see wrong models or no models

**Solutions**:
1. Check user profile exists and has correct tier
2. Verify model configuration logic in browser console
3. Test with preview feature in admin interface

### RLS Policies Issues

**Problem**: Permission denied errors

**Solutions**:
1. Ensure RLS policies are created correctly
2. Check if user is authenticated properly
3. Verify policy conditions match your use case

## Database Schema Reference

### user_profiles Table

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Reference to auth.users |
| email | TEXT | User email (unique) |
| full_name | TEXT | User's full name |
| role | TEXT | User role (admin, manager, user) |
| permissions | JSONB | Custom permissions object |
| tier | TEXT | User tier (free, basic, premium) |
| is_active | BOOLEAN | Account status |
| created_at | TIMESTAMP | Profile creation time |
| updated_at | TIMESTAMP | Last update time |

### Permissions Structure

```json
{
  "models": {
    "o1-preview": true,
    "gpt-4o": false,
    "claude-3-opus": true
  },
  "features": {
    "admin_panel": true,
    "export_data": true,
    "bulk_operations": false
  },
  "limits": {
    "monthly_queries": 1000,
    "concurrent_requests": 5
  }
}
```

## Security Notes

1. **RLS is Enabled**: All user profile access is controlled by Row Level Security
2. **Admin Only Operations**: Role changes and permission updates require admin access
3. **Audit Trail**: All profile changes are timestamped
4. **Secure Defaults**: New users get minimal permissions by default

## Next Steps

1. **Test the System**: Create test users with different tiers
2. **Configure Your Clients**: Set up real client accounts with appropriate access
3. **Monitor Usage**: Use the admin dashboard to track system usage
4. **Customize as Needed**: Modify tiers and permissions based on your business model

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify database queries in Supabase logs
3. Test with different user accounts
4. Review the RLS policies if permission errors occur

The system is now ready for production use with proper admin controls and client management!
