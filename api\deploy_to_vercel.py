import os
import subprocess
import json
import sys
import shutil
from pathlib import Path

def check_vercel_cli():
    """Check if Vercel CLI is installed"""
    try:
        subprocess.run(["vercel", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_vercel_cli():
    """Install Vercel CLI"""
    print("Installing Vercel CLI...")
    try:
        subprocess.run(["npm", "install", "-g", "vercel"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing Vercel CLI: {e}")
        return False

def prepare_environment_file():
    """Prepare environment file for Vercel deployment"""
    env_file = Path("../.env")
    vercel_env_file = Path("./.vercel/.env")
    
    if not env_file.exists():
        print("Warning: .env file not found in project root. Creating an empty one.")
        with open(env_file, "w") as f:
            f.write("# Environment variables for the Market Research Tool\n")
    
    # Create .vercel directory if it doesn't exist
    os.makedirs("./.vercel", exist_ok=True)
    
    # Copy .env file to .vercel directory
    shutil.copy(env_file, vercel_env_file)
    print(f"Copied environment file to {vercel_env_file}")

def deploy_to_vercel():
    """Deploy the FastAPI application to Vercel"""
    if not check_vercel_cli():
        print("Vercel CLI not found. Attempting to install...")
        if not install_vercel_cli():
            print("Failed to install Vercel CLI. Please install it manually with 'npm install -g vercel'")
            return False
    
    print("Preparing environment for Vercel deployment...")
    prepare_environment_file()
    
    print("\nDeploying to Vercel...")
    try:
        # Run vercel command to deploy
        subprocess.run(["vercel", "--prod"], check=True)
        print("\n✅ Deployment successful!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Deployment failed: {e}")
        return False

def main():
    """Main function"""
    print("Market Research Tool - FastAPI Deployment Script")
    print("===============================================")
    
    # Check if we're in the api directory
    if not os.path.exists("main.py") or not os.path.exists("vercel.json"):
        print("Error: This script must be run from the api directory containing main.py and vercel.json")
        sys.exit(1)
    
    # Deploy to Vercel
    if deploy_to_vercel():
        print("\nYour FastAPI application has been deployed to Vercel!")
        print("You can now update your frontend to use the new API endpoints.")
    else:
        print("\nDeployment failed. Please check the errors above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
