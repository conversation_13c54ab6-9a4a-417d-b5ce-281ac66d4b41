from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class Question(BaseModel):
    """
    Represents a single question in a questionnaire.
    """
    text: str
    category: Optional[str] = None  # e.g., demographic, psychographic, behavioral
    type: Optional[str] = None      # e.g., open, multiple-choice
    options: Optional[List[str]] = None

class Questionnaire(BaseModel):
    """
    Represents a full questionnaire.
    """
    title: str
    description: Optional[str] = None
    questions: List[Question]

class QuestionnaireResponse(BaseModel):
    """
    Represents a response to a questionnaire.
    """
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    questionnaire: str
    timestamp: datetime = Field(default_factory=datetime.now)
    responses: Dict[str, Any]
    user_info: Optional[Dict[str, Any]] = None