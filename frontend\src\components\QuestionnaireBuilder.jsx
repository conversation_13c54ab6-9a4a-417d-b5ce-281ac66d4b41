import React, { useState, useEffect } from 'react';
import yaml from 'js-yaml';
import axios from 'axios';
import { DateInput, RatingInput, FileUploadInput, SliderInput } from './AdvancedInputs';
import config from '../config';

/**
 * QuestionnaireBuilder component
 *
 * Loads YAML questionnaires from the public folder, renders them dynamically, and allows saving responses.
 */
function QuestionnaireBuilder() {
  const [filename, setFilename] = useState('');
  const [files, setFiles] = useState([]);
  const [questionnaire, setQuestionnaire] = useState(null);
  const [responses, setResponses] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState({});

  // Function to fetch available YAML files
  const fetchYamlFiles = async () => {
    try {
      setLoading(true);
      // First try to fetch from backend
      let backendFiles = [];
      try {
        const response = await axios.get(config.endpoints.yaml.list);
        if (response.data && response.data.files) {
          backendFiles.push(...response.data.files);
        }
      } catch (err) {
        console.warn('Could not fetch files from backend:', err);
      }
      
      // Then scan the public folder for YAML files
      const publicFiles = await scanPublicFolderForYamlFiles();
      
      // Add known files that might not be detected by the scanner
      const knownFiles = [
        '00-strategy-questionnaire.yaml',
        '01-spiritual-fine-jewelry-survey.yaml',
        '03-ideal-customer-profile.yaml',
        '04-customer-acquisition-strategy.yaml',
        'spiritual-jewelry-questionnaire.yaml'
      ];
      
      // Combine all sources and remove duplicates
      const allFiles = [...new Set([...backendFiles, ...publicFiles, ...knownFiles])];
      console.log('All available files:', allFiles);
      setFiles(allFiles);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching YAML files:', err);
      setError('Failed to load available questionnaires');
      setLoading(false);
    }
  };
  
  // Fetch available YAML files on component mount
  useEffect(() => {
    fetchYamlFiles();
  }, []);
  
  // Function to scan the public folder for YAML files
  const scanPublicFolderForYamlFiles = async () => {
    try {
      // Get the list of files in the public directory
      const response = await fetch('/');
      const html = await response.text();
      
      // Create a temporary DOM element to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Find all links that might be YAML files
      const links = Array.from(doc.querySelectorAll('a'));
      
      // Log all found links for debugging
      console.log('All links found:', links.map(link => link.getAttribute('href')));
      
      // Filter for YAML, YML, and YALM files
      const yamlFiles = links
        .map(link => link.getAttribute('href'))
        .filter(href => {
          if (!href) return false;
          const lowerHref = href.toLowerCase();
          return lowerHref.endsWith('.yaml') || lowerHref.endsWith('.yml') || lowerHref.endsWith('.yalm');
        })
        .map(href => href.split('/').pop()); // Get just the filename
      
      console.log('YAML files found:', yamlFiles);
      
      // Also try to directly fetch known files
      const knownFilesToCheck = [
        '00-strategy-questionnaire.yaml',
        '01-spiritual-fine-jewelry-survey.yaml',
        '03-ideal-customer-profile.yaml',
        '04-customer-acquisition-strategy.yaml',
        'advanced-features-demo.yaml',
        'spiritual-jewelry-questionnaire.yaml'
      ];
      
      for (const file of knownFilesToCheck) {
        try {
          const directResponse = await fetch(`/${file}`);
          if (directResponse.ok && !yamlFiles.includes(file)) {
            console.log(`Found ${file} via direct fetch`);
            yamlFiles.push(file);
          }
        } catch (directErr) {
          console.warn(`Could not directly fetch file ${file}:`, directErr);
        }
      }
      
      return yamlFiles;
    } catch (err) {
      console.error('Error scanning public folder:', err);
      return [];
    }
  };

  // Fetch and parse YAML when a file is selected
  useEffect(() => {
    if (!filename) {
      setQuestionnaire(null);
      setResponses({});
      return;
    }
    setLoading(true);
    setError('');
    fetch(`/${filename}`)
      .then(res => {
        if (!res.ok) throw new Error('Failed to load YAML file');
        return res.text();
      })
      .then(text => {
        try {
          const data = yaml.load(text);
          if (!data || !data.sections) {
            throw new Error('Invalid YAML structure: missing sections');
          }
          
          setQuestionnaire(data);
          // Initialize responses with empty values
          const initial = {};
          data.sections.forEach(section => {
            section.questions.forEach(q => {
              if (q.type === 'checkbox') initial[q.id] = [];
              else initial[q.id] = '';
            });
          });
          setResponses(initial);
          setValidationErrors({});
        } catch (yamlError) {
          console.error('YAML parsing error:', yamlError);
          setError(`Failed to parse YAML file: ${yamlError.message}`);
        }
      })
      .catch(err => {
        console.error('File loading error:', err);
        setError(`Failed to load file: ${err.message}`);
      })
      .finally(() => setLoading(false));
  }, [filename]);

  // Handle input changes
  const handleChange = (id, value, type) => {
    setResponses(prev => {
      if (type === 'checkbox') {
        const arr = prev[id] || [];
        if (arr.includes(value)) {
          return { ...prev, [id]: arr.filter(v => v !== value) };
        } else {
          return { ...prev, [id]: [...arr, value] };
        }
      }
      return { ...prev, [id]: value };
    });
  };

  // Validate form before submission
  const validateForm = () => {
    const errors = {};
    let isValid = true;
    
    if (!questionnaire) return false;
    
    questionnaire.sections.forEach(section => {
      section.questions.forEach(q => {
        if (q.required) {
          const response = responses[q.id];
          if (!response || 
              (Array.isArray(response) && response.length === 0) || 
              (typeof response === 'string' && response.trim() === '')) {
            errors[q.id] = 'This field is required';
            isValid = false;
          }
        }
      });
    });
    
    setValidationErrors(errors);
    return isValid;
  };

  // Save responses locally as a JSON file
  const handleLocalSave = () => {
    if (!validateForm()) {
      setSaveMessage('Please fill in all required fields');
      setSaveSuccess(false);
      return;
    }
    
    const blob = new Blob([
      JSON.stringify({
        questionnaire: questionnaire.title,
        responses,
        timestamp: new Date().toISOString(),
      }, null, 2)
    ], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${questionnaire.title.replace(/\s+/g, '_').toLowerCase()}_responses.json`;
    a.click();
    URL.revokeObjectURL(url);
    setSaveSuccess(true);
    setSaveMessage('Responses saved locally! (Check your downloads)');
    setTimeout(() => {
      setSaveSuccess(false);
      setSaveMessage('');
    }, 3000);
  };
  
  // Submit responses to the backend
  const handleSubmit = async () => {
    if (!validateForm()) {
      setSaveMessage('Please fill in all required fields');
      setSaveSuccess(false);
      return;
    }
    
    try {
      setLoading(true);
      const formattedResponses = Object.keys(responses).reduce((acc, key) => {
        if (Array.isArray(responses[key])) {
          acc[key] = responses[key].join(', ');
        } else {
          acc[key] = responses[key];
        }
        return acc;
      }, {});
      
      // Submit to API
      try {
        const apiResponse = await axios.post(config.endpoints.responses.save, {
          questionnaire: questionnaire.title,
          responses: formattedResponses,
          timestamp: new Date().toISOString(),
        });
        
        if (apiResponse.data && apiResponse.data.success) {
          setSaveSuccess(true);
          setSaveMessage(`Responses submitted successfully! ID: ${apiResponse.data.id}`);
          setTimeout(() => {
            setSaveSuccess(false);
            setSaveMessage('');
          }, 3000);
        }
      } catch (err) {
        console.error('Error submitting responses:', err);
        setError('Failed to submit responses to the server');
        setSaveSuccess(false);
        setTimeout(() => {
          setSaveSuccess(false);
          setSaveMessage('');
        }, 3000);
      }
    } catch (err) {
      console.error('Error submitting responses:', err);
      setError('Failed to submit responses to the server');
      setSaveSuccess(false);
    } finally {
      setLoading(false);
    }
  };



// Render input based on question type
const renderInput = (q) => {
  switch (q.type) {
    case 'text':
      return (
        <input
          type="text"
          id={q.id}
          value={responses[q.id] || ''}
          placeholder={q.placeholder || ''}
          className="w-full border border-gray-300 rounded px-3 py-2 body-text focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
          onChange={(e) => handleChange(q.id, e.target.value, q.type)}
          required={q.required}
        />
      );
    case 'textarea':
      return (
        <textarea
          id={q.id}
          value={responses[q.id] || ''}
          placeholder={q.placeholder || ''}
          className="w-full border border-gray-300 rounded px-3 py-2 h-24 body-text focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
          onChange={(e) => handleChange(q.id, e.target.value, q.type)}
          required={q.required}
        />
      );
    case 'radio':
      return (
        <div className="space-y-2">
          {q.options.map((option, i) => (
            <div key={i} className="flex items-center">
              <input
                type="radio"
                id={`${q.id}-${i}`}
                name={q.id}
                value={option}
                checked={responses[q.id] === option}
                className="mr-2 text-blue-500 focus:ring-blue-400"
                onChange={(e) => handleChange(q.id, e.target.value, q.type)}
                required={q.required && i === 0}
              />
              <label htmlFor={`${q.id}-${i}`} className="body-text">{option}</label>
            </div>
          ))}
        </div>
      );
    case 'checkbox':
      return (
        <div className="space-y-2">
          {q.options.map((option, i) => (
            <div key={i} className="flex items-center">
              <input
                type="checkbox"
                id={`${q.id}-${i}`}
                name={q.id}
                value={option}
                checked={responses[q.id]?.includes(option) || false}
                className="mr-2 text-blue-500 focus:ring-blue-400 rounded"
                onChange={(e) => {
                  const currentValues = [...(responses[q.id] || [])];
                  if (e.target.checked) {
                    currentValues.push(option);
                  } else {
                    const index = currentValues.indexOf(option);
                    if (index !== -1) currentValues.splice(index, 1);
                  }
                  handleChange(q.id, currentValues, q.type);
                }}
                required={q.required && i === 0 && responses[q.id]?.length === 0}
              />
              <label htmlFor={`${q.id}-${i}`} className="body-text">{option}</label>
            </div>
          ))}
        </div>
      );
    case 'select':
      return (
        <select
          id={q.id}
          value={responses[q.id] || ''}
          className="w-full border border-gray-300 rounded px-3 py-2 body-text focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
          onChange={(e) => handleChange(q.id, e.target.value, q.type)}
          required={q.required}
        >
          <option value="">-- Select an option --</option>
          {q.options.map((option, i) => (
            <option key={i} value={option}>{option}</option>
          ))}
        </select>
      );
    case 'date':
      return (
        <DateInput
          id={q.id}
          value={responses[q.id] || ''}
          required={q.required}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded px-3 py-2 body-text focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
        />
      );
    case 'rating':
      return (
        <RatingInput
          id={q.id}
          value={parseInt(responses[q.id] || 0, 10)}
          required={q.required}
          onChange={handleChange}
          max={q.max || 5}
          className="text-blue-500"
        />
      );
    case 'file':
      return (
        <FileUploadInput
          id={q.id}
          value={responses[q.id] || null}
          required={q.required}
          onChange={handleChange}
          accept={q.accept || '*'}
          className="body-text"
        />
      );
    case 'slider':
      return (
        <SliderInput
          id={q.id}
          value={parseInt(responses[q.id] || 0, 10)}
          required={q.required}
          onChange={handleChange}
          min={q.min || 0}
          max={q.max || 100}
          step={q.step || 1}
          className="accent-blue-500"
        />
      );
    default:
      return <span className="text-gray-400 body-text">Unsupported type: {q.type}</span>;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded shadow mt-8">
      <h2 className="raleway-title-h1 mb-4">QUESTIONNAIRE TO LOAD</h2>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-1">
          <label className="block raleway-menu">Select:</label>
          {loading && <span className="text-blue-500 text-sm animate-pulse">Refreshing files...</span>}
        </div>
        <div className="relative">
          <select
            className="w-full border rounded px-3 py-2 body-text"
            value={filename}
            onChange={e => setFilename(e.target.value)}
            onClick={() => fetchYamlFiles()}
            onFocus={() => fetchYamlFiles()}
          >
            <option value="">-- Choose a file --</option>
            {files.map(f => (
              <option key={f} value={f}>{f}</option>
            ))}
          </select>
          <button 
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              fetchYamlFiles();
            }}
            title="Refresh file list"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
      {loading && filename && <div className="body-text">Loading questionnaire...</div>}
      {error && <div className="text-red-600 mt-2 body-text">{error}</div>}
      {questionnaire && (
        <form className="space-y-8 mt-6" onSubmit={e => e.preventDefault()}>
          <div className="mb-4">
            <h3 className="raleway-title-h2 mb-1">{questionnaire.title}</h3>
            <p className="body-text mb-2">{questionnaire.description}</p>
          </div>
          {questionnaire.sections.map((section, i) => (
            <div key={i} className="mb-6 p-4 bg-white rounded shadow-sm">
              <h4 className="raleway-title-h3 mb-2">{section.title}</h4>
              <div className="space-y-4">
                {section.questions.map(q => (
                  <div key={q.id} className="mb-2 p-3 border-l-2 border-blue-100">
                    <label className="block raleway-menu mb-1">{q.text}{q.required && <span className="text-red-500">*</span>}</label>
                    {q.explanation && <p className="text-sm body-text mb-2 text-gray-500">{q.explanation}</p>}
                    {renderInput(q)}
                    {validationErrors[q.id] && (
                      <p className="text-red-500 text-sm mt-1 body-text">{validationErrors[q.id]}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
          <div className="flex space-x-4 mt-6">
            <button
              type="button"
              className="omega-nav-btn"
              onClick={handleLocalSave}
              disabled={loading}
            >
              Save Locally
            </button>
            <button
              type="button"
              className="booknow-btn"
              onClick={handleSubmit}
              disabled={loading}
            >
              Submit to Server
            </button>
          </div>
          {saveSuccess && <div className="text-green-600 mt-2 body-text">{saveMessage}</div>}
          {Object.keys(validationErrors).length > 0 && (
            <div className="text-red-500 mt-4 p-2 border border-red-300 rounded bg-red-50">
              <p className="raleway-menu">Please fix the following errors:</p>
              <ul className="list-disc pl-5">
                {Object.keys(validationErrors).map(key => (
                  <li key={key} className="body-text">{validationErrors[key]}</li>
                ))}
              </ul>
            </div>
          )}
        </form>
      )}
    </div>
  );
}

export default QuestionnaireBuilder;