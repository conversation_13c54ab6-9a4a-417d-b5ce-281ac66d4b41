from http.server import BaseHTTPRequestHandler
import json
import os
import sys
import re
import yaml

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils import get_questionnaires_dir, get_public_dir, parse_markdown_to_questionnaire
from models import Questionnaire

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Extract path and query parameters
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        # Get action and id from query parameters
        action = query_params.get('action', [None])[0]
        file_id = query_params.get('id', [None])[0]
        
        try:
            # Handle different endpoints based on action parameter
            if action == 'list' or parsed_url.path == '/list':
                # List all questionnaires
                self._handle_list()
            elif action == 'generate' or '/generate/' in parsed_url.path:
                # Generate a questionnaire
                if file_id is None and '/generate/' in parsed_url.path:
                    # Extract file_id from path if not in query params
                    path_parts = parsed_url.path.split('/')
                    file_id = path_parts[-1] if len(path_parts) > 2 else None
                
                if file_id:
                    self._handle_generate(file_id)
                else:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Missing file_id parameter"}).encode())
            elif file_id or (parsed_url.path != '/' and parsed_url.path != ''):
                # Get a specific questionnaire by ID
                if file_id is None:
                    # Extract file_id from path if not in query params
                    path_parts = parsed_url.path.split('/')
                    file_id = path_parts[-1] if len(path_parts) > 1 else None
                
                if file_id:
                    self._handle_get_by_id(file_id)
                else:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Missing file_id parameter"}).encode())
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Not found"}).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def do_POST(self):
        # Extract path and query parameters
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        # Get action from query parameters
        action = query_params.get('action', [None])[0]
        
        try:
            # Handle different endpoints based on action parameter
            if action == 'save' or parsed_url.path == '/save':
                # Save a questionnaire
                self._handle_save()
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Not found"}).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def _handle_list(self):
        """Handle listing all questionnaires"""
        questionnaires_dir = get_questionnaires_dir()
        files = []
        
        # Get files from the questionnaires directory
        if os.path.exists(questionnaires_dir):
            files = [f for f in os.listdir(questionnaires_dir) if f.endswith('.json')]
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps({"files": files}).encode())

    def _handle_get_by_id(self, file_id):
        """Handle getting a specific questionnaire by ID"""
        questionnaires_dir = get_questionnaires_dir()
        file_path = os.path.join(questionnaires_dir, f"{file_id}.json")
        
        if not os.path.exists(file_path):
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Questionnaire not found"}).encode())
            return
        
        with open(file_path, 'r') as f:
            questionnaire = json.load(f)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(questionnaire).encode())

    def _handle_generate(self, file_id):
        """Handle generating a questionnaire from a YAML file"""
        public_dir = get_public_dir()
        questionnaires_dir = get_questionnaires_dir()
        
        yaml_file = os.path.join(public_dir, f"{file_id}.yaml")
        if not os.path.exists(yaml_file):
            yaml_file = os.path.join(public_dir, f"{file_id}.yml")
            if not os.path.exists(yaml_file):
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "YAML file not found"}).encode())
                return
        
        try:
            with open(yaml_file, 'r') as f:
                yaml_content = yaml.safe_load(f)
            
            # Convert YAML to questionnaire format
            questionnaire = {
                "title": yaml_content.get("title", "Questionnaire"),
                "description": yaml_content.get("description", ""),
                "sections": []
            }
            
            for section in yaml_content.get("sections", []):
                section_data = {
                    "title": section.get("title", ""),
                    "questions": []
                }
                
                for question in section.get("questions", []):
                    question_data = {
                        "id": question.get("id", f"q{len(section_data['questions'])+1}"),
                        "text": question.get("text", ""),
                        "type": question.get("type", "text"),
                        "required": question.get("required", False),
                        "options": question.get("options", []),
                        "explanation": question.get("explanation", "")
                    }
                    section_data["questions"].append(question_data)
                
                questionnaire["sections"].append(section_data)
            
            # Save the generated questionnaire
            output_file = os.path.join(questionnaires_dir, f"{file_id}.json")
            with open(output_file, 'w') as f:
                json.dump(questionnaire, f)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            self.wfile.write(json.dumps(questionnaire).encode())
        
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())
            return

    def _handle_save(self):
        """Handle saving a questionnaire"""
        questionnaires_dir = get_questionnaires_dir()
        
        # Read request body
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        questionnaire = json.loads(post_data.decode())
        
        # Generate a filename if not provided
        if 'filename' not in questionnaire:
            title = questionnaire.get('title', 'questionnaire')
            filename = re.sub(r'[^\w\-_]', '_', title.lower()) + '.json'
        else:
            filename = questionnaire['filename']
            del questionnaire['filename']
        
        # Ensure filename has .json extension
        if not filename.endswith('.json'):
            filename += '.json'
        
        # Save the questionnaire
        file_path = os.path.join(questionnaires_dir, filename)
        with open(file_path, 'w') as f:
            json.dump(questionnaire, f)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps({"success": True, "filename": filename}).encode())
