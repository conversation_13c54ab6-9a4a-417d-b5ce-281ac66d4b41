# Migration Guide: Vercel Serverless Functions to FastAPI

This guide will help you migrate your existing Vercel serverless functions to a FastAPI Python application. The migration preserves all existing functionality while providing improved performance, better documentation, and easier maintenance.

## Table of Contents

1. [Overview](#overview)
2. [Benefits of FastAPI](#benefits-of-fastapi)
3. [Local Development](#local-development)
4. [Testing](#testing)
5. [Deployment to Vercel](#deployment-to-vercel)
6. [Updating Frontend Code](#updating-frontend-code)
7. [Database Considerations](#database-considerations)
8. [Troubleshooting](#troubleshooting)

## Overview

The migration involves replacing the current serverless functions with a unified FastAPI application. The key files are:

- `main.py` - The main FastAPI application
- `vercel.json` - Configuration for Vercel deployment
- `requirements.txt` - Python dependencies
- `test_fastapi.py` - Script to test the API endpoints
- `deploy_to_vercel.py` - Helper script for deployment
- `update_frontend_api.js` - Script to help update frontend API references

The API endpoints maintain the same URL structure as the previous implementation, so minimal changes are needed in your frontend code.

## Benefits of FastAPI

- **Performance**: FastAPI is one of the fastest Python frameworks available
- **Type Checking**: Built-in request and response validation with Pydantic
- **Documentation**: Automatic interactive API documentation (Swagger UI and ReDoc)
- **Modern Python**: Uses modern Python features (type hints, async/await)
- **Dependency Injection**: Built-in dependency injection system
- **Middleware Support**: Easy to add middleware for CORS, authentication, etc.

## Local Development

### Prerequisites

- Python 3.7+ installed
- pip (Python package manager)

### Setup

1. Install dependencies:

```bash
cd api
pip install -r requirements.txt
```

2. Run the FastAPI server:

```bash
python main.py
```

The server will start on http://localhost:3001 with auto-reload enabled.

3. Access the API documentation:
   - Swagger UI: http://localhost:3001/docs
   - ReDoc: http://localhost:3001/redoc

## Testing

Use the included test script to verify that all endpoints are working correctly:

```bash
cd api
python test_fastapi.py
```

This script will:
1. Check if the FastAPI server is running
2. Test each API endpoint
3. Display the results

## Deployment to Vercel

### Prerequisites

- Node.js and npm installed
- Vercel CLI (will be installed by the deployment script if needed)
- Vercel account

### Deployment Steps

1. Run the deployment script:

```bash
cd api
python deploy_to_vercel.py
```

2. Follow the prompts from the Vercel CLI:
   - Log in to your Vercel account if prompted
   - Select the project to deploy to (or create a new one)
   - Confirm the deployment

3. Once deployed, Vercel will provide you with a URL for your API.

### Manual Deployment

If you prefer to deploy manually:

1. Install Vercel CLI:

```bash
npm install -g vercel
```

2. Deploy to Vercel:

```bash
cd api
vercel --prod
```

## Updating Frontend Code

The FastAPI implementation maintains the same API endpoints as your previous serverless functions, so minimal changes should be needed in your frontend code.

Run the helper script to identify API references in your frontend code:

```bash
cd api
node update_frontend_api.js
```

This script will:
1. Scan your frontend code for API references
2. List all found references
3. Provide guidance on what changes might be needed

### Common Updates

- If using absolute URLs, update them to point to your new FastAPI deployment
- If using environment variables for API URLs, update those variables
- Test all functionality to ensure it works with the new API

## Database Considerations

The current implementation uses file-based storage in the `/tmp` directory, which is suitable for Vercel's serverless environment. However, this is not persistent storage.

For a production environment, consider:

1. Using a database like Supabase (as mentioned in your project memories)
2. Updating the utility functions in `utils.py` to use the database instead of file storage
3. Adding proper user authentication and authorization

Remember that your project had a schema issue where the questionnaire_responses table was missing a user_id column. Make sure to address this when setting up your database.

## Troubleshooting

### Common Issues

1. **CORS Errors**: If you encounter CORS errors, check that the CORS middleware in `main.py` is configured correctly.

2. **Missing Dependencies**: If you get import errors, ensure all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

3. **Deployment Failures**: If deployment to Vercel fails:
   - Check that your `vercel.json` file is correctly configured
   - Verify that all dependencies are listed in `requirements.txt`
   - Check Vercel logs for specific error messages

4. **API Endpoint Not Found**: If an endpoint returns 404:
   - Verify the URL path is correct
   - Check that the endpoint is defined in `main.py`
   - Ensure you're using the correct HTTP method (GET, POST, etc.)

### Getting Help

If you encounter issues not covered in this guide:
1. Check the FastAPI documentation: https://fastapi.tiangolo.com/
2. Check the Vercel documentation: https://vercel.com/docs
3. Search for similar issues on Stack Overflow

## Next Steps

After successfully migrating to FastAPI, consider these improvements:

1. Implement proper database integration with Supabase
2. Add authentication and authorization
3. Enhance the Prompt Management system for AI prompts
4. Improve error handling and logging
5. Add unit and integration tests

---

This migration preserves all your existing functionality while providing a more robust, maintainable, and well-documented API for your Market Research Tool.
