// Simple script to debug .env file loading
const fs = require('fs');
const dotenv = require('dotenv');
const path = require('path');

console.log('Current working directory:', process.cwd());

// Try to read the .env file directly
const envPath = path.resolve(__dirname, '.env');
console.log('Attempting to read .env file from:', envPath);

try {
  const envFileContent = fs.readFileSync(envPath, 'utf8');
  console.log('\n.env file exists and contains:');
  // Show the file content with line numbers for debugging
  const lines = envFileContent.split('\n');
  lines.forEach((line, index) => {
    // Skip empty lines
    if (line.trim()) {
      console.log(`Line ${index + 1}: ${line}`);
    }
  });
} catch (error) {
  console.error('Error reading .env file:', error.message);
}

// Now try to load it with dotenv
console.log('\nLoading with dotenv:');
const result = dotenv.config({ path: envPath });

if (result.error) {
  console.error('Error loading .env file with dotenv:', result.error);
} else {
  console.log('.env file loaded successfully with dotenv');
}

// Check if the environment variables are set
console.log('\nEnvironment variables after dotenv.config():');
console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY);
console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY);
console.log('google_api_key:', process.env.google_api_key);
console.log('deepseek_api_key:', process.env.deepseek_api_key);

// Try manual parsing as a last resort
console.log('\nTrying manual parsing of .env file:');
try {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  const manualEnv = {};
  envLines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        const key = match[1].trim();
        const value = match[2].trim();
        manualEnv[key] = value;
        console.log(`${key}: ${value}`);
      }
    }
  });
  
  console.log('\nManually parsed environment variables:');
  console.log(manualEnv);
} catch (error) {
  console.error('Error manually parsing .env file:', error.message);
}
