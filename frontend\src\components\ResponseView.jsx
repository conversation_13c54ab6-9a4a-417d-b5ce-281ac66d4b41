import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { supabase } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

/**
 * ResponseView component
 * 
 * Displays the questionnaire responses in a readable format
 */
function ResponseView() {
  const [allResponses, setAllResponses] = useState([]);
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user } = useAuth();
  
  useEffect(() => {
    const fetchResponses = async () => {
      setLoading(true);
      
      try {
        // First check sessionStorage for immediate viewing after submission
        const storedData = sessionStorage.getItem('questionnaire_view_responses');
        
        if (storedData) {
          // Use sessionStorage data if available (most recent submission)
          const responseData = JSON.parse(storedData);
          console.log('Using response data from sessionStorage:', responseData);
          setSelectedResponse(responseData);
          // Clear sessionStorage to avoid showing the same response on refresh
          // sessionStorage.removeItem('questionnaire_view_responses');
        }
        
        // Always fetch all responses from Supabase for the current user
        if (user) {
          console.log('Fetching all responses from Supabase for user:', user.id);
          
          const { data, error } = await supabase
            .from('questionnaire_responses')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
          
          if (error) {
            console.error('Error fetching from Supabase:', error);
            throw new Error('Failed to fetch responses from database: ' + error.message);
          }
          
          console.log('Supabase response data:', data);
          
          if (data && data.length > 0) {
            setAllResponses(data);
            
            // If no response is selected yet, select the first one
            if (!selectedResponse) {
              const firstResponse = {
                questionnaire: data[0].questionnaire_name,
                responses: data[0].responses,
                timestamp: data[0].created_at,
                rawData: data[0]
              };
              setSelectedResponse(firstResponse);
            }
          } else {
            // If no responses found for this user, try without user filter as fallback
            console.log('No responses found for this user, trying without user filter');
            const { data: allData, error: allError } = await supabase
              .from('questionnaire_responses')
              .select('*')
              .order('created_at', { ascending: false });
              
            if (allError) {
              console.error('Error fetching all responses:', allError);
            } else if (allData && allData.length > 0) {
              console.log('Found responses without user filter:', allData);
              setAllResponses(allData);
              
              // If no response is selected yet, select the first one
              if (!selectedResponse) {
                const firstResponse = {
                  questionnaire: allData[0].questionnaire_name,
                  responses: allData[0].responses,
                  timestamp: allData[0].created_at,
                  rawData: allData[0]
                };
                setSelectedResponse(firstResponse);
              }
            } else {
              console.log('No responses found in Supabase at all');
              setError('No questionnaire responses found. Please complete a questionnaire first.');
            }
          }
        } else {
          // Not authenticated and no sessionStorage data
          console.log('No data in sessionStorage and user not authenticated');
          setError('Please log in to view your questionnaire responses.');
        }
      } catch (err) {
        console.error('Error loading responses:', err);
        setError('Failed to load questionnaire responses: ' + err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchResponses();
  }, [user]); // Re-run when user changes
  
  // Handle selecting a response
  const handleSelectResponse = (response) => {
    const formattedResponse = {
      questionnaire: response.questionnaire_name,
      responses: response.responses,
      timestamp: response.created_at,
      rawData: response
    };
    setSelectedResponse(formattedResponse);
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown Date';
    
    try {
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      }) + ' at ' + date.toLocaleTimeString('en-US');
    } catch (err) {
      return 'Date Error';
    }
  };
  
  // Format responses for display
  const formatSelectedResponse = () => {
    if (!selectedResponse) return null;
    
    const { questionnaire, responses: responseData, timestamp } = selectedResponse;
    
    // If responseData is not an object, show error
    if (!responseData || typeof responseData !== 'object') {
      return (
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
          <h3 className="font-semibold text-yellow-800 mb-2">Invalid Response Format</h3>
          <p className="text-yellow-700">The response data is not in the expected format.</p>
          <pre className="mt-2 text-xs bg-yellow-100 p-2 rounded overflow-auto">
            {JSON.stringify(selectedResponse, null, 2)}
          </pre>
        </div>
      );
    }
    
    return (
      <div>
        <h2 className="text-xl font-semibold mb-4 text-blue-700">
          Responses for: {questionnaire}
        </h2>
        
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h3 className="font-semibold text-gray-700 mb-2">Submission Details</h3>
          <p>Submitted on: {formatDate(selectedResponse.timestamp)}</p>
          {selectedResponse.rawData && (
            <div className="mt-2 text-xs text-gray-500">
              <p>Response ID: {selectedResponse.rawData.id}</p>
              {selectedResponse.rawData.user_id && <p>User ID: {selectedResponse.rawData.user_id}</p>}
            </div>
          )}
        </div>
        
        <div className="space-y-6">
          {Object.entries(responseData).map(([questionId, answer], index) => {
            // Format the question ID to be more readable
            const formattedQuestionId = questionId
              .replace(/_/g, ' ')
              .replace(/([A-Z])/g, ' $1')
              .replace(/^./, str => str.toUpperCase());
              
            return (
              <div key={index} className="p-4 bg-white rounded-lg shadow-sm">
                <p className="font-medium text-gray-800 mb-2">{formattedQuestionId}</p>
                <div className="pl-4 border-l-2 border-blue-200">
                  {typeof answer === 'object' ? (
                    <pre className="text-sm bg-gray-50 p-2 rounded overflow-auto">
                      {JSON.stringify(answer, null, 2)}
                    </pre>
                  ) : (
                    <p className="text-gray-700">{answer || '(No answer provided)'}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6 text-blue-800">Questionnaire Responses</h1>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 bg-white p-6 rounded-lg shadow-md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Loading your responses...</p>
        </div>
      ) : error ? (
        <div className="bg-red-50 p-6 rounded-lg border border-red-200 mb-6 shadow-md">
          <h3 className="font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-red-700">{error}</p>
          <button 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            onClick={() => navigate('/market-research')}
          >
            Back to Questionnaires
          </button>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row gap-6">
          {/* List of responses */}
          <div className="w-full md:w-1/3 bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 className="text-xl font-semibold mb-4 text-blue-700">Your Responses</h2>
            
            {allResponses.length === 0 ? (
              <p className="text-gray-600">No responses found. Please complete a questionnaire first.</p>
            ) : (
              <div className="space-y-4">
                {allResponses.map((response) => (
                  <div 
                    key={response.id} 
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${selectedResponse?.rawData?.id === response.id 
                      ? 'bg-blue-50 border border-blue-200' 
                      : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'}`}
                    onClick={() => handleSelectResponse(response)}
                  >
                    <p className="font-medium">{response.questionnaire_name}</p>
                    <p className="text-sm text-gray-600">{formatDate(response.created_at)}</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {response.questionnaire_type}
                      </span>
                      {response.user_id === user?.id && (
                        <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded">Your response</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            <div className="mt-6">
              <button 
                className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition"
                onClick={() => navigate('/market-research')}
              >
                Back to Questionnaires
              </button>
            </div>
          </div>
          
          {/* Selected response details */}
          <div className="w-full md:w-2/3 bg-white p-6 rounded-lg shadow-md mb-8">
            {selectedResponse ? (
              <>
                {formatSelectedResponse()}
                
                <div className="flex justify-end mt-8">
                  <button 
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
                    onClick={() => navigate('/strategy')}
                  >
                    View Marketing Strategy
                  </button>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <p>Select a response from the list to view details</p>
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="mt-4 text-center">
        <Link 
          to="/response-debug" 
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          Advanced Debugging Tools
        </Link>
      </div>
    </div>
  );
}

export default ResponseView;
