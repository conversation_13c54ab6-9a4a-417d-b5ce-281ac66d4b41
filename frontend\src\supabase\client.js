import { createClient } from '@supabase/supabase-js';

// Using hardcoded values for now
const supabaseUrl = 'https://rtctcrbetkdievwkrrfr.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ0Y3RjcmJldGtkaWV2d2tycmZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NjQxMzMsImV4cCI6MjA2NDQ0MDEzM30.V001m5G6uYVCOlPZNY46zlVw7RHSY_tgPS8TqyDWiSk';

console.log('Connecting to Supabase with URL:', supabaseUrl);

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Helper functions for questionnaire responses

/**
 * Save questionnaire responses to Supabase
 * @param {string} questionnaireType - Type of questionnaire (e.g., 'consumerInsight')
 * @param {string} questionnaireName - Human-readable name of questionnaire
 * @param {object} responses - The questionnaire responses
 * @returns {Promise} - Supabase insert result
 */
export async function saveQuestionnaireResponses(questionnaireType, questionnaireName, responses) {
  try {
    // Get current user
    console.log('Saving questionnaire responses - Type:', questionnaireType, 'Name:', questionnaireName);
    console.log('Responses data structure:', typeof responses, Array.isArray(responses) ? 'Array' : 'Object');
    
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Error getting current user:', authError);
      return { data: null, error: authError };
    }
    
    const user = authData?.user;
    console.log('Current user:', user ? `ID: ${user.id}, Email: ${user.email}` : 'Not authenticated');
    
    // Prepare the record to insert
    const record = { 
      questionnaire_type: questionnaireType,
      questionnaire_name: questionnaireName,
      responses: responses,
      created_at: new Date().toISOString(),
      // Now that the user_id column exists, include it in the record
      user_id: user?.id || null
    };
    
    // Log whether user ID is being included
    if (user?.id) {
      console.log('Including user_id in record:', user.id);
    } else {
      console.log('No user_id available, saving without user association');
    }
    
    console.log('Inserting record:', record);
    
    // Insert the record
    const { data, error } = await supabase
      .from('questionnaire_responses')
      .insert([record]);
      
    if (error) {
      console.error('Error saving questionnaire responses to Supabase:', error);
      
      // More detailed error logging
      if (error.code === '42501' || error.message.includes('permission')) {
        console.error('This appears to be a permissions issue. Check your Row Level Security (RLS) policies.');
      }
      
      if (error.code === '23503') {
        console.error('This appears to be a foreign key constraint issue.');
      }
      
      if (error.code === '42P01') {
        console.error('The table "questionnaire_responses" might not exist.');
      }
    } else {
      console.log('Successfully saved questionnaire response to Supabase!');
    }
    
    return { data, error };
  } catch (err) {
    console.error('Exception when saving questionnaire responses:', err);
    throw err;
  }
}

/**
 * Get questionnaire responses from Supabase
 * @param {string} questionnaireType - Type of questionnaire (e.g., 'consumerInsight')
 * @param {boolean} userOnly - Whether to only get responses for the current user
 * @returns {Promise} - Supabase query result
 */
export async function getQuestionnaireResponses(questionnaireType, userOnly = false) {
  try {
    console.log('Getting questionnaire responses for type:', questionnaireType, 'userOnly:', userOnly);
    
    // Get current user if filtering by user
    let userId = null;
    if (userOnly) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
      console.log('Current user ID for filtering:', userId);
    }
    
    // Start query
    let query = supabase
      .from('questionnaire_responses')
      .select('*');
      
    // Filter by questionnaire type if provided
    if (questionnaireType) {
      query = query.eq('questionnaire_type', questionnaireType);
    }
    
    // Filter by user if requested and user is authenticated
    if (userOnly && userId) {
      query = query.eq('user_id', userId);
    }
    
    // Execute query with ordering
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error getting questionnaire responses:', error);
    } else {
      console.log('Successfully retrieved responses:', data?.length || 0);
    }
    
    return { data, error };
  } catch (err) {
    console.error('Exception when getting questionnaire responses:', err);
    throw err;
  }
}

/**
 * Check if a questionnaire has been completed
 * @param {string} questionnaireType - Type of questionnaire
 * @param {boolean} userOnly - Whether to only check for the current user
 * @returns {Promise<boolean>} - Whether the questionnaire has been completed
 */
export async function hasCompletedQuestionnaire(questionnaireType, userOnly = false) {
  try {
    // Get current user if filtering by user
    let userId = null;
    if (userOnly) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
    }
    
    // Start query
    let query = supabase
      .from('questionnaire_responses')
      .select('id')
      .eq('questionnaire_type', questionnaireType);
      
    // Filter by user if requested and user is authenticated
    if (userOnly && userId) {
      query = query.eq('user_id', userId);
    }
    
    // Execute query
    const { data, error } = await query.limit(1);
    
    if (error) {
      console.error('Error checking questionnaire completion:', error);
      return { completed: false, error };
    }
    
    return { completed: data && data.length > 0, error: null };
  } catch (error) {
    console.error('Error checking questionnaire completion:', error);
    return { completed: false, error };
  }
}

/**
 * Get all completed questionnaires
 * @param {boolean} userOnly - Whether to only get questionnaires for the current user
 * @returns {Promise} - Supabase query result with all unique questionnaire types
 */
export async function getAllCompletedQuestionnaires(userOnly = false) {
  try {
    // Get current user if filtering by user
    let userId = null;
    if (userOnly) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
    }
    
    // Start query
    let query = supabase
      .from('questionnaire_responses')
      .select('questionnaire_type');
      
    // Filter by user if requested and user is authenticated
    if (userOnly && userId) {
      query = query.eq('user_id', userId);
    }
    
    // Execute query with ordering
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error getting completed questionnaires:', error);
      return { completionStatus: null, error };
    }
    
    // Extract unique questionnaire types
    const uniqueTypes = [...new Set(data.map(item => item.questionnaire_type))];
    
    // Create a completion status object
    const completionStatus = {
      consumerInsight: uniqueTypes.includes('consumerInsight'),
      segmentation: uniqueTypes.includes('segmentation'),
      trends: uniqueTypes.includes('trends'),
      focusGroups: uniqueTypes.includes('focusGroups')
    };
    
    return { completionStatus, error: null };
  } catch (error) {
    console.error('Error getting completed questionnaires:', error);
    return { completionStatus: null, error };
  }
}
