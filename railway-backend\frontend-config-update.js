/**
 * Frontend Configuration Update for Railway Backend
 * 
 * This file shows how to update your frontend configuration to use the Railway backend.
 * Copy the relevant parts to your frontend/src/config.js file.
 */

// Example configuration for frontend/src/config.js
const config = {
  // Railway Backend URL (replace with your actual Railway deployment URL)
  RAILWAY_API_BASE_URL: 'https://your-railway-app.railway.app',
  
  // Local development URL
  LOCAL_API_BASE_URL: 'http://localhost:8000',
  
  // Vercel API URL (for non-LLM operations)
  VERCEL_API_BASE_URL: 'https://your-vercel-app.vercel.app/api',
  
  // Determine which API to use based on environment
  get LLM_API_BASE_URL() {
    // Use Railway for LLM operations
    return process.env.NODE_ENV === 'development' 
      ? this.LOCAL_API_BASE_URL 
      : this.RAILWAY_API_BASE_URL;
  },
  
  get API_BASE_URL() {
    // Use Vercel for general API operations
    return this.VERCEL_API_BASE_URL;
  },
  
  endpoints: {
    // LLM endpoints (Railway backend)
    llm: {
      gemini: `${this.LLM_API_BASE_URL}/api/gemini`,
      openai: `${this.LLM_API_BASE_URL}/api/openai`,
      deepseek: `${this.LLM_API_BASE_URL}/api/deepseek`,
      anthropic: `${this.LLM_API_BASE_URL}/api/anthropic`,
      groq: `${this.LLM_API_BASE_URL}/api/groq`,
    },
    
    // Regular endpoints (Vercel backend)
    questionnaire: {
      list: `${this.API_BASE_URL}/questionnaire/list`,
      get: (id) => `${this.API_BASE_URL}/questionnaire/${id}`,
      save: `${this.API_BASE_URL}/questionnaire/save`,
      generate: `${this.API_BASE_URL}/questionnaire/generate`,
    },
    
    responses: {
      list: `${this.API_BASE_URL}/responses/list`,
      get: (id) => `${this.API_BASE_URL}/responses/${id}`,
      save: `${this.API_BASE_URL}/responses/save`,
      download: (id) => `${this.API_BASE_URL}/responses/download/${id}`,
    },
    
    yaml: {
      list: `${this.API_BASE_URL}/yaml/list`,
    }
  }
};

export default config;

/**
 * Usage in your React components:
 * 
 * // For LLM operations, use the Railway backend
 * const response = await fetch(config.endpoints.llm.openai, {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({
 *     prompt: "Your question here",
 *     model: "gpt-4o",
 *     thinkingMode: false
 *   })
 * });
 * 
 * // For regular operations, use the Vercel backend
 * const questionnaires = await fetch(config.endpoints.questionnaire.list);
 */
