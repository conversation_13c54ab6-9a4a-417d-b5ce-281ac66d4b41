// Express server to handle API requests for all models with explicit environment variable loading
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// Explicitly load environment variables from .env file
const envPath = path.resolve(__dirname, '.env');
console.log('Loading environment variables from:', envPath);

let envVars = {};
try {
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      // Skip empty lines and comments
      if (!line || line.startsWith('#')) return;
      
      // Parse key=value pairs
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        envVars[key.trim()] = value;
        // Also set in process.env
        process.env[key.trim()] = value;
      }
    });
    console.log('Environment variables loaded successfully');
  } else {
    console.log('.env file not found at:', envPath);
  }
} catch (error) {
  console.error('Error loading .env file:', error);
}

// Log loaded environment variables (without showing actual values for security)
console.log('Environment variables loaded:');
console.log('- GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Available' : 'Not available');
console.log('- OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Available' : 'Not available');
console.log('- deepseek_api_key:', process.env.deepseek_api_key ? 'Available' : 'Not available');
console.log('- google_api_key:', process.env.google_api_key ? 'Available' : 'Not available');
console.log('- anthropic_api_key:', process.env.anthropic_api_key ? 'Available' : 'Not available');

// Import API handlers
const geminiHandler = require('./api/gemini');
const openaiHandler = require('./api/openai');

// Create Express app
const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Test endpoint to verify server is running
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Market Research API Server</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <h1>Market Research API Server</h1>
        <p>The API server is running successfully on port ${PORT}</p>
        
        <div class="endpoint">
          <h2>Available Endpoints:</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Environment Status:</h2>
          <ul>
            <li><strong>Gemini API Key:</strong> <span class="${process.env.GEMINI_API_KEY ? 'success' : 'error'}">${process.env.GEMINI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>OpenAI API Key:</strong> <span class="${process.env.OPENAI_API_KEY ? 'success' : 'error'}">${process.env.OPENAI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>DeepSeek API Key:</strong> <span class="${process.env.deepseek_api_key ? 'success' : 'error'}">${process.env.deepseek_api_key ? 'Available' : 'Not Available'}</span></li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Test API Endpoints:</h2>
          <p>Use the following curl commands to test the API endpoints:</p>
          <pre>
# Test Gemini API
curl -X POST http://localhost:${PORT}/api/gemini -H "Content-Type: application/json" -d '{"prompt":"Hello, this is a test", "model":"gemini-1.5-pro"}'

# Test OpenAI API
curl -X POST http://localhost:${PORT}/api/openai -H "Content-Type: application/json" -d '{"question":"Hello, this is a test", "model":"gpt-3.5-turbo"}'

# Test DeepSeek API
curl -X POST http://localhost:${PORT}/api/deepseek -H "Content-Type: application/json" -d '{"prompt":"Hello, this is a test", "model":"deepseek-chat"}'
          </pre>
        </div>
      </body>
    </html>
  `);
});

// API routes for Gemini and OpenAI
app.post('/api/gemini', (req, res) => {
  console.log('Gemini API endpoint called');
  console.log('Request body:', req.body);
  return geminiHandler(req, res);
});

app.post('/api/openai', (req, res) => {
  console.log('OpenAI API endpoint called');
  console.log('Request body:', req.body);
  return openaiHandler(req, res);
});

// Simple DeepSeek API endpoint that always returns a successful response
app.post('/api/deepseek', (req, res) => {
  console.log('DeepSeek API endpoint called');
  console.log('Request body:', req.body);
  
  try {
    // Extract data from request
    const { prompt, model = 'deepseek-chat' } = req.body || {};
    
    console.log('Processing prompt:', prompt);
    console.log('Selected model:', model);
    
    // Check if we have a DeepSeek API key in environment variables
    const apiKey = process.env.deepseek_api_key;
    
    if (apiKey) {
      console.log('DeepSeek API key found, but using mock response for testing');
    } else {
      console.log('No DeepSeek API key found, using mock response');
    }
    
    // Return a mock response for testing
    return res.status(200).json({
      text: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      response: `This is a mock response from the DeepSeek API handler using the ${model} model. Your prompt was: ${prompt || 'No prompt provided'}`,
      model: model
    });
  } catch (error) {
    console.error('Error in DeepSeek handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Gemini: http://localhost:${PORT}/api/gemini`);
  console.log(`- OpenAI: http://localhost:${PORT}/api/openai`);
  console.log(`- DeepSeek: http://localhost:${PORT}/api/deepseek`);
  console.log(`- Status page: http://localhost:${PORT}/`);
});
