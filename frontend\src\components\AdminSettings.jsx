import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import ModelConfigAdmin from './ModelConfigAdmin';

/**
 * Admin Settings Page
 * Centralized admin interface for managing the application
 */
export default function AdminSettings() {
  const { user, userProfile, isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('models');
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    totalResponses: 0,
    activeClients: 0,
    lastUpdated: new Date().toISOString()
  });

  // Check if user has admin access (using database-driven user profile system)
  const hasAdminAccess = isAdmin();

  useEffect(() => {
    if (hasAdminAccess) {
      loadSystemStats();
    }
  }, [hasAdminAccess]);

  const loadSystemStats = async () => {
    // In a real implementation, you would fetch these from your API
    // For now, we'll use mock data
    try {
      // Example API calls:
      // const users = await fetch('/api/admin/users/count');
      // const responses = await fetch('/api/admin/responses/count');
      
      setSystemStats({
        totalUsers: 42,
        totalResponses: 156,
        activeClients: 12,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to load system stats:', error);
    }
  };

  if (!hasAdminAccess) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <svg className="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h2 className="text-xl text-red-800 mb-2">Access Denied</h2>
              <p className="text-red-700">You don't have permission to access the admin settings page.</p>
              <p className="text-red-600 text-sm mt-1">Contact your administrator if you believe this is an error.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'overview',
      name: 'Overview',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: 'models',
      name: 'Model Configuration',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    },
    {
      id: 'users',
      name: 'User Management',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      )
    },
    {
      id: 'system',
      name: 'System Settings',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
        </svg>
      )
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="p-6 bg-white rounded shadow-md text-center">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">System Overview</h3>
        <p className="body-text mb-6">Welcome to the admin dashboard. Here you can manage all aspects of your market research tool.</p>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600">Total Users</p>
              <p className="text-2xl font-bold text-blue-900">{systemStats.totalUsers}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600">Total Responses</p>
              <p className="text-2xl font-bold text-green-900">{systemStats.totalResponses}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-center">
            <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <div className="ml-4">
              <p className="text-sm font-medium text-purple-600">Active Clients</p>
              <p className="text-2xl font-bold text-purple-900">{systemStats.activeClients}</p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
          <div className="flex items-center">
            <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <div className="ml-4">
              <p className="text-sm font-medium text-orange-600">System Status</p>
              <p className="text-lg font-bold text-orange-900">Healthy</p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setActiveTab('models')}
            className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200"
          >
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Configure Models</h4>
            <p className="body-text">Manage AI model access for clients</p>
          </button>

          <button
            onClick={() => setActiveTab('users')}
            className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200"
          >
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Manage Users</h4>
            <p className="body-text">View and manage user accounts</p>
          </button>

          <button
            onClick={() => setActiveTab('system')}
            className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200"
          >
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">System Settings</h4>
            <p className="body-text">Configure application settings</p>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center text-sm">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            <span className="body-text">New user registered: <EMAIL></span>
            <span className="text-gray-400 ml-auto text-xs">2 hours ago</span>
          </div>
          <div className="flex items-center text-sm">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            <span className="body-text">Model configuration <NAME_EMAIL></span>
            <span className="text-gray-400 ml-auto text-xs">4 hours ago</span>
          </div>
          <div className="flex items-center text-sm">
            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
            <span className="body-text">Strategy generated using GPT-4o</span>
            <span className="text-gray-400 ml-auto text-xs">6 hours ago</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUserManagement = () => (
    <div className="space-y-6">
      <div className="p-6 bg-white rounded shadow-md text-center">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">User Management</h3>
        <p className="body-text mb-6">Manage user accounts, permissions, and access levels.</p>
      </div>

      {/* Current User Info */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Current Admin User</h3>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-700">
            <div>
              <p className="body-text"><strong>Email:</strong> {user?.email}</p>
              <p className="body-text"><strong>Role:</strong> {userProfile?.role || 'Loading...'}</p>
              <p className="body-text"><strong>Tier:</strong> {userProfile?.tier || 'Loading...'}</p>
            </div>
            <div>
              <p className="body-text"><strong>Profile Created:</strong> {userProfile?.created_at ? new Date(userProfile.created_at).toLocaleDateString() : 'Loading...'}</p>
              <p className="body-text"><strong>Last Updated:</strong> {userProfile?.updated_at ? new Date(userProfile.updated_at).toLocaleString() : 'Loading...'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick User Actions */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">View Supabase Dashboard</h4>
            <p className="body-text">Access user management in Supabase</p>
          </button>

          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Export User List</h4>
            <p className="body-text">Download user data for analysis</p>
          </button>
        </div>
      </div>

      <div className="p-6 bg-white rounded shadow-md">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="body-text text-yellow-800">Advanced user management features are coming soon. For now, users are managed through Supabase Auth.</p>
          </div>
        </div>

        {/* Planned Features */}
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Planned Features</h3>
        <ul className="space-y-2">
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">View all registered users with detailed profiles</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Manage user permissions and role assignments</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Set usage quotas and API rate limits</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Bulk user operations and CSV import/export</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">User activity monitoring and analytics</span>
          </li>
        </ul>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="p-6 bg-white rounded shadow-md text-center">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">System Settings</h3>
        <p className="body-text mb-6">Configure global application settings and preferences.</p>
      </div>

      {/* Current Configuration */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Current Configuration</h3>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-green-700">
            <div>
              <p className="body-text"><strong>Environment:</strong> {window.location.hostname === 'localhost' ? 'Development' : 'Production'}</p>
              <p className="body-text"><strong>Frontend:</strong> Vercel</p>
              <p className="body-text"><strong>Backend:</strong> Railway (LLM) + Vercel (API)</p>
            </div>
            <div>
              <p className="body-text"><strong>Database:</strong> Supabase</p>
              <p className="body-text"><strong>Authentication:</strong> Supabase Auth</p>
              <p className="body-text"><strong>File Storage:</strong> Local/Browser</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Health */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">System Health</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2"></div>
            <p className="raleway-title-h3 text-sm text-green-800 mb-1">Frontend</p>
            <p className="body-text text-green-600">Operational</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2"></div>
            <p className="raleway-title-h3 text-sm text-green-800 mb-1">Railway API</p>
            <p className="body-text text-green-600">Operational</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2"></div>
            <p className="raleway-title-h3 text-sm text-green-800 mb-1">Database</p>
            <p className="body-text text-green-600">Operational</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 bg-white rounded shadow-md">
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Clear Cache</h4>
            <p className="body-text">Clear browser cache and local storage</p>
          </button>

          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Export Logs</h4>
            <p className="body-text">Download system logs for debugging</p>
          </button>

          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Test API Connections</h4>
            <p className="body-text">Verify all API endpoints are working</p>
          </button>

          <button className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-all text-center bg-purple-50 hover:bg-purple-100 border-purple-200">
            <h4 className="raleway-title-h3 text-sm text-purple-800 mb-2">Backup Configuration</h4>
            <p className="body-text">Export current system configuration</p>
          </button>
        </div>
      </div>

      <div className="p-6 bg-white rounded shadow-md">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="body-text text-yellow-800">Advanced system settings features are coming soon. Current settings are managed through environment variables and configuration files.</p>
          </div>
        </div>

        {/* Planned Features */}
        <h3 className="raleway-title-h3 mb-4 text-purple-700">Planned Features</h3>
        <ul className="space-y-2">
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Dynamic API endpoint configuration</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Global default model settings</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Rate limiting and usage quotas</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Automated backup and maintenance schedules</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Email notification settings</span>
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <span className="body-text">Performance monitoring and alerts</span>
          </li>
        </ul>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Admin Settings</h2>
        <p className="body-text mb-4">
          Manage your market research tool configuration and system settings.
        </p>
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mb-6">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Last updated: {new Date(systemStats.lastUpdated).toLocaleString()}</span>
        </div>

        {/* Tab Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          {tabs.map((tab, index) => {
            const colors = [
              { bg: 'bg-blue-50', hover: 'hover:bg-blue-100', active: 'bg-blue-100', text: 'text-blue-700', border: 'border-blue-300' },
              { bg: 'bg-green-50', hover: 'hover:bg-green-100', active: 'bg-green-100', text: 'text-green-700', border: 'border-green-300' },
              { bg: 'bg-purple-50', hover: 'hover:bg-purple-100', active: 'bg-purple-100', text: 'text-purple-700', border: 'border-purple-300' },
              { bg: 'bg-orange-50', hover: 'hover:bg-orange-100', active: 'bg-orange-100', text: 'text-orange-700', border: 'border-orange-300' }
            ];
            const colorSet = colors[index % colors.length];

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all ${
                  activeTab === tab.id
                    ? `${colorSet.active} ${colorSet.border} ${colorSet.text}`
                    : `${colorSet.bg} ${colorSet.hover} ${colorSet.text}`
                }`}
              >
                <h3 className="text-xs font-medium uppercase tracking-wide">{tab.name}</h3>
              </button>
            );
          })}
        </div>
      </div>


      {/* Content */}
      <div className="w-full max-w-4xl mx-auto">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'models' && <ModelConfigAdmin />}
        {activeTab === 'users' && renderUserManagement()}
        {activeTab === 'system' && renderSystemSettings()}
      </div>
    </div>
  );
}
