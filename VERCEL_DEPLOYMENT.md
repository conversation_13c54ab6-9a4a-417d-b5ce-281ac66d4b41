# Vercel Deployment Guide

## Serverless Function Optimization

Vercel's free tier has a limit of 12 serverless functions. To stay within this limit, the API endpoints have been consolidated as follows:

### Consolidated API Structure

1. **api/index.py**
   - Root API endpoint (`/api`)
   - YAML file listing (`/api/yaml`)

2. **api/questionnaire.py**
   - List questionnaires (`/api/questionnaire?action=list`)
   - Get questionnaire by ID (`/api/questionnaire?action=[file_id]`)
   - Generate questionnaire from YAML (`/api/questionnaire?action=generate&id=[file_id]`)
   - Save questionnaire (`/api/questionnaire?action=save`)

3. **api/responses.py**
   - List responses (`/api/responses?action=list`)
   - Get response by ID (`/api/responses?action=[file_id]`)
   - Download response (`/api/responses?action=download&id=[file_id]`)
   - Save response (`/api/responses?action=save`)

### URL Routing

The `vercel.json` file has been configured to route requests to the appropriate serverless function based on the URL pattern. For compatibility with both path-based and query parameter-based routing, the handlers support both formats:

- Path-based: `/api/questionnaire/list`
- Query parameter-based: `/api/questionnaire?action=list`

### Deployment

To deploy to Vercel:

1. Make sure all dependencies are listed in `requirements.txt`
2. Push your changes to GitHub
3. Connect your GitHub repository to Vercel
4. Configure the build settings to use the `build.js` script
5. Deploy the application

### Local Development

For local development, the API test server (`test-api.js`) will continue to work with the existing path-based routing. No changes are needed to the frontend code as the API endpoints remain the same from the client's perspective.

To run locally:
1. Start the API server: `npm run test-api` from the root directory
2. Start the frontend dev server: `npm run dev` from the frontend directory

## File Structure

```
api/
├── index.py          # Root API + YAML listing
├── models.py         # Shared models
├── questionnaire.py  # All questionnaire endpoints
├── responses.py      # All response endpoints
└── utils.py          # Shared utilities
```

This consolidated structure reduces the number of serverless functions from 12+ to just 4, well within Vercel's free tier limits.
