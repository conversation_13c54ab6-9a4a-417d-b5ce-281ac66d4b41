import os
import sys
import json
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure the Gemini API
api_key = os.getenv("GEMINI_API_KEY")

if not api_key:
    print("Status: 500")
    print("Content-Type: application/json")
    print("")
    print(json.dumps({"error": "GEMINI_API_KEY not configured"}))
    sys.exit(1)

genai.configure(api_key=api_key)

def main():
    # Get the request method
    method = os.environ.get("REQUEST_METHOD", "").upper()
    
    if method != "POST":
        print("Status: 405")
        print("Content-Type: application/json")
        print("")
        print(json.dumps({"error": "Method not allowed"}))
        return
    
    # Get the request body
    try:
        body = os.environ.get("BODY", "{}")
        data = json.loads(body)
        question = data.get("question", "")
        
        if not question:
            print("Status: 400")
            print("Content-Type: application/json")
            print("")
            print(json.dumps({"error": "Question is required"}))
            return
        
        # Configure the model
        model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Generate a response
        response = model.generate_content(
            f"You are a helpful AI assistant specializing in marketing and client acquisition strategies. "
            f"Please provide a helpful response to the following question: {question}"
        )
        
        # Return the response
        print("Status: 200")
        print("Content-Type: application/json")
        print("")
        print(json.dumps({"answer": response.text}))
        
    except Exception as e:
        print("Status: 500")
        print("Content-Type: application/json")
        print("")
        print(json.dumps({"error": str(e)}))

if __name__ == "__main__":
    main()
