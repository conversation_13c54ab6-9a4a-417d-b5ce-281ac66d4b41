<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Omega Praxis - Market Research Tool</title>
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #333; }
    .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .success { color: green; }
    .warning { color: orange; }
    .error { color: red; }
    a { color: #0066cc; text-decoration: none; }
    a:hover { text-decoration: underline; }
  </style>
</head>
<body>
  <h1>Market Research Tool - Diagnostic Page</h1>
  
  <div class="card">
    <h2>Original Frontend</h2>
    <p>The original frontend is trying to load from these paths:</p>
    <ul>
      <li>CSS: <code>/frontend/dist/assets/index-a5765f4a.css</code></li>
      <li>JavaScript: <code>/frontend/dist/assets/main-434ec9eb.js</code></li>
    </ul>
    <p class="warning">⚠️ These paths might not be accessible in the Railway deployment environment.</p>
  </div>
  
  <div class="card">
    <h2>API Endpoints</h2>
    <p>Try accessing these endpoints to diagnose the issue:</p>
    <ul>
      <li><a href="/api">/api</a> - Basic API status</li>
      <li><a href="/api/healthcheck">/api/healthcheck</a> - Health check endpoint</li>
      <li><a href="/api/diagnostic">/api/diagnostic</a> - Detailed diagnostic information</li>
      <li><a href="/api-status">/api-status</a> - API status page</li>
    </ul>
  </div>
  
  <div class="card">
    <h2>Troubleshooting</h2>
    <p>If you're seeing this page instead of the Market Research Tool, there might be an issue with:</p>
    <ol>
      <li>The frontend build files not being included in the Railway deployment</li>
      <li>The paths being different in the Railway environment</li>
      <li>A configuration issue specific to Railway</li>
    </ol>
    <p>Check the Railway logs for more information.</p>
  </div>
  
  <div id="root"></div>
  
  <!-- Try to load the original scripts with relative paths as a fallback -->
  <script>
    // Log the current location for debugging
    console.log('Current location:', window.location.href);
    console.log('Pathname:', window.location.pathname);
    
    // Try to load the original assets
    const tryLoadAssets = () => {
      try {
        // Try different path variations
        const paths = [
          '/frontend/dist/assets/main-434ec9eb.js',
          './frontend/dist/assets/main-434ec9eb.js',
          '../frontend/dist/assets/main-434ec9eb.js',
          'frontend/dist/assets/main-434ec9eb.js'
        ];
        
        paths.forEach(path => {
          const script = document.createElement('script');
          script.type = 'module';
          script.src = path;
          script.onerror = () => console.error(`Failed to load script from: ${path}`);
          script.onload = () => console.log(`Successfully loaded script from: ${path}`);
          document.body.appendChild(script);
        });
      } catch (error) {
        console.error('Error loading assets:', error);
      }
    };
    
    // Execute after a short delay
    setTimeout(tryLoadAssets, 500);
  </script>
</body>
</html>
