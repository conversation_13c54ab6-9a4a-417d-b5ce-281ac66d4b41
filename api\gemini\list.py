import os
import json

def main():
    # Get the request method
    method = os.environ.get("REQUEST_METHOD", "").upper()
    
    if method != "GET":
        print("Status: 405")
        print("Content-Type: application/json")
        print()
        print(json.dumps({"error": "Method not allowed"}))
        return
    
    # Return information about Gemini capabilities
    capabilities = [
        {
            "id": "market-research",
            "name": "Market Research",
            "description": "Ask questions about market research methodologies and best practices"
        },
        {
            "id": "client-acquisition",
            "name": "Client Acquisition Strategies",
            "description": "Get insights on effective client acquisition strategies"
        },
        {
            "id": "competitive-analysis",
            "name": "Competitive Analysis",
            "description": "Learn how to analyze competitors and identify market opportunities"
        },
        {
            "id": "marketing-trends",
            "name": "Marketing Trends",
            "description": "Stay updated on the latest marketing trends and innovations"
        }
    ]
    
    print("Status: 200")
    print("Content-Type: application/json")
    print()
    print(json.dumps({"capabilities": capabilities}))

if __name__ == "__main__":
    main()
