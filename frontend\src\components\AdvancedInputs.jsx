import React from 'react';

/**
 * DateInput component for date selection
 * @param {Object} props - Component props
 * @param {string} props.id - Question ID
 * @param {string} props.value - Current value
 * @param {boolean} props.required - Whether the field is required
 * @param {Function} props.onChange - Change handler function
 */
export const DateInput = ({ id, value, required, onChange }) => {
  return (
    <input
      type="date"
      className="border rounded px-2 py-1 w-full"
      value={value || ''}
      required={required}
      onChange={(e) => onChange(id, e.target.value, 'date')}
    />
  );
};

/**
 * RatingInput component for star ratings
 * @param {Object} props - Component props
 * @param {string} props.id - Question ID
 * @param {number} props.value - Current value
 * @param {boolean} props.required - Whether the field is required
 * @param {Function} props.onChange - Change handler function
 * @param {number} props.max - Maximum rating value (default: 5)
 */
export const RatingInput = ({ id, value, required, onChange, max = 5 }) => {
  const stars = Array.from({ length: max }, (_, i) => i + 1);
  
  return (
    <div className="flex items-center">
      {stars.map((star) => (
        <button
          key={star}
          type="button"
          className={`text-2xl focus:outline-none ${
            star <= value ? 'text-yellow-500' : 'text-gray-300'
          }`}
          onClick={() => onChange(id, star, 'rating')}
          aria-label={`Rate ${star} out of ${max}`}
        >
          ★
        </button>
      ))}
      {required && value === 0 && (
        <span className="text-red-500 ml-2 text-sm">Required</span>
      )}
    </div>
  );
};

/**
 * FileUploadInput component for file uploads
 * @param {Object} props - Component props
 * @param {string} props.id - Question ID
 * @param {File} props.value - Current file
 * @param {boolean} props.required - Whether the field is required
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.accept - Accepted file types
 */
export const FileUploadInput = ({ id, value, required, onChange, accept = "*" }) => {
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      onChange(id, file, 'file');
    }
  };

  return (
    <div className="flex flex-col space-y-2">
      <input
        type="file"
        className="border rounded px-2 py-1 w-full"
        accept={accept}
        required={required}
        onChange={handleFileChange}
      />
      {value && (
        <div className="text-sm text-gray-600">
          Selected file: {value.name} ({Math.round(value.size / 1024)} KB)
        </div>
      )}
    </div>
  );
};

/**
 * SliderInput component for range selection
 * @param {Object} props - Component props
 * @param {string} props.id - Question ID
 * @param {number} props.value - Current value
 * @param {boolean} props.required - Whether the field is required
 * @param {Function} props.onChange - Change handler function
 * @param {number} props.min - Minimum value
 * @param {number} props.max - Maximum value
 * @param {number} props.step - Step value
 */
export const SliderInput = ({ id, value, required, onChange, min = 0, max = 100, step = 1 }) => {
  return (
    <div className="flex flex-col space-y-2">
      <div className="flex justify-between">
        <span className="text-sm">{min}</span>
        <span className="text-sm font-medium">{value || min}</span>
        <span className="text-sm">{max}</span>
      </div>
      <input
        type="range"
        className="w-full"
        min={min}
        max={max}
        step={step}
        value={value || min}
        required={required}
        onChange={(e) => onChange(id, parseInt(e.target.value, 10), 'slider')}
      />
    </div>
  );
};
