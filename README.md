# Market Research Tool for AI Consulting Clients

A modular, minimalist web application to help clients conduct marketing research, create and fill questionnaires, and analyze responses. Built with React (frontend) and Python serverless functions (backend) with Tailwind CSS, focusing on clean UX and extensibility. Optimized for deployment on Vercel.

## Features
- Create and manage YAML-based questionnaires with multiple question types
- Support for various input types: text, textarea, radio, checkbox, ranking, date, rating, file upload, and slider
- Form validation with user feedback for required fields
- Save responses locally or submit to server
- View and download saved questionnaire responses
- Clean, responsive UI built with Tailwind CSS
- Backend API for storing and retrieving questionnaire responses

## Folder Structure
```
/api                 # Python serverless functions for Vercel deployment
/docs                # Methodology, strategy, and reference markdown files
/frontend            # React (Vite) frontend app
  /public            # Static files including YAML questionnaire templates
  /src               # React components and application code
/backend             # Original FastAPI backend (not used in Vercel deployment)
/data                # User-generated files (questionnaires and responses)
/tests               # Pytest unit tests
```

## Setup (Development)

1. **Install Dependencies:**
   - Python 3.10+
   - Node.js 18+
   - Install Python dependencies: 
     ```bash
     pip install -r requirements.txt
     ```
   - Install Node.js dependencies:
     ```bash
     npm install
     cd frontend
     npm install
     ```

2. **Run Development Server:**
   - Start the frontend:
     ```bash
     cd frontend
     npm run dev
     ```
   - The application will be available at `http://localhost:5173`
   
   Note: For local API testing, you'll need to run a local server for the API endpoints.

## Usage

### Creating Questionnaires
1. Create a YAML file in the `frontend/public/` directory following the structure in the example questionnaires
2. The questionnaire will automatically appear in the dropdown menu on the frontend

### Questionnaire Structure
```yaml
title: Your Questionnaire Title
description: A detailed description of the questionnaire
sections:
  - title: Section Title
    questions:
      - id: unique_question_id
        text: The question text
        type: text|textarea|radio|checkbox|ranking|date|rating|file|slider
        required: true|false
        # Additional fields based on type:
        placeholder: Placeholder text for text inputs
        options: [Option1, Option2] # For radio, checkbox, ranking
        max: 5 # For rating, slider
        min: 0 # For slider
        step: 1 # For slider
```

### Supported Question Types
- **text**: Single-line text input
- **textarea**: Multi-line text input
- **radio**: Single-choice selection
- **checkbox**: Multiple-choice selection
- **ranking**: Ordered selection from options
- **date**: Date picker
- **rating**: Star rating (1-5 by default)
- **file**: File upload
- **slider**: Range slider

## API Endpoints

### Questionnaires
- `GET /api/questionnaire/list`: List all questionnaires
- `GET /api/questionnaire/{file_id}`: Get a specific questionnaire
- `GET /api/yaml/list`: List all YAML questionnaire files

### Responses
- `POST /api/responses/save`: Save a questionnaire response
- `GET /api/responses/list`: List all saved responses
- `GET /api/responses/{file_id}`: Get a specific response
- `GET /api/responses/download/{file_id}`: Download a response as JSON

## Deployment on Vercel

1. **Prerequisites:**
   - A Vercel account
   - Git repository with your code

2. **Deploy to Vercel:**
   - Push your code to GitHub
   - Log in to Vercel and create a new project
   - Connect your GitHub repository
   - Configure the project:
     - Build Command: `npm run build`
     - Output Directory: `dist`
     - Install Command: `npm install`
   - Deploy

3. **Environment Variables:**
   - No environment variables are required for basic functionality
   - For production, you might want to set up database connections or other services

## Contributing
- Follow PEP8, use type hints, and format with `black` (backend)
- Use ESLint and Prettier for frontend code formatting
- Use consistent naming and modular structure
- Add/mark tasks in `TASK.md`

## License
MIT