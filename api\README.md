# Serverless API Functions

This directory contains Python serverless functions designed to be deployed on Vercel. Each function handles a specific API endpoint and follows a consistent pattern for request handling and response formatting.

## Directory Structure

```
/api
  /index.py                # Root API endpoint
  /models.py               # Shared data models
  /utils.py                # Shared utility functions
  /questionnaire           # Questionnaire-related endpoints
    /generate.py           # Generate questionnaire from markdown
    /list.py               # List all questionnaires
    /save.py               # Save a questionnaire
    /[file_id].py          # Get a specific questionnaire
  /responses               # Response-related endpoints
    /list.py               # List all responses
    /save.py               # Save a questionnaire response
    /[file_id].py          # Get a specific response
    /download              # Download endpoints
      /[file_id].py        # Download a response as JSON
  /yaml                    # YAML-related endpoints
    /list.py               # List all YAML questionnaire files
```

## Function Pattern

Each serverless function follows this general pattern:

```python
from http.server import BaseHTTPRequestHandler
import json
import os
import sys

# Import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from api.utils import set_cors_headers, get_body, ensure_data_dir
from api.models import SomeModel

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Set CORS headers
        set_cors_headers(self)
        
        # Process request
        # ...
        
        # Send response
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode())
        
    def do_POST(self):
        # Similar pattern for POST requests
        # ...
```

## Data Storage

In the Vercel serverless environment, data is stored in the `/tmp` directory, which is ephemeral but provides temporary storage during function execution. The `ensure_data_dir` utility function creates necessary directories:

- `/tmp/data/questionnaires/` - For storing questionnaire JSON files
- `/tmp/data/responses/` - For storing questionnaire response JSON files

## Testing Locally

To test these functions locally, use the provided test script:

```bash
npm run test-api
```

This will start a local server that simulates the Vercel serverless environment and routes requests to the appropriate Python functions.
