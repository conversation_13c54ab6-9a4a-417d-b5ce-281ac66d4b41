const http = require('http');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m'
};

console.log(`${colors.blue}Starting API test server for Market Research Tool...${colors.reset}`);

// Create data directories if they don't exist
const dataDir = path.join(__dirname, 'data');
const questionnairesDir = path.join(dataDir, 'questionnaires');
const responsesDir = path.join(dataDir, 'responses');

if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}
if (!fs.existsSync(questionnairesDir)) {
  fs.mkdirSync(questionnairesDir, { recursive: true });
}
if (!fs.existsSync(responsesDir)) {
  fs.mkdirSync(responsesDir, { recursive: true });
}

// Create a simple HTTP server to test API endpoints
const server = http.createServer((req, res) => {
  console.log(`${colors.yellow}Request received:${colors.reset} ${req.method} ${req.url}`);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  // Parse the URL to determine which API endpoint to call
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathSegments = url.pathname.split('/').filter(Boolean);
  
  if (pathSegments[0] !== 'api') {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not found' }));
    return;
  }
  
  // Determine the Python file to execute based on the URL
  let pythonFile = '';
  let pythonArgs = [];
  
  // Check for serverless function endpoints first
  const SERVERLESS_FUNCTION_ENDPOINTS = ['gemini', 'save-document', 'openai'];
  if (pathSegments.length === 2 && SERVERLESS_FUNCTION_ENDPOINTS.includes(pathSegments[1])) {
    // These will be handled in the req.on('end') handler
    // Just set up an empty pythonFile to avoid errors
    pythonFile = '';
  } else if (pathSegments.length === 1) {
    // Root API endpoint
    pythonFile = path.join(__dirname, 'api', 'index.py');
  } else if (pathSegments.length === 2) {
    // API endpoint with one segment (e.g., /api/yaml)
    pythonFile = path.join(__dirname, 'api', pathSegments[1], 'list.py');
  } else if (pathSegments.length === 3) {
    // API endpoint with two segments (e.g., /api/questionnaire/list)
    if (pathSegments[2] === 'list') {
      pythonFile = path.join(__dirname, 'api', pathSegments[1], 'list.py');
    } else if (pathSegments[2] === 'save') {
      pythonFile = path.join(__dirname, 'api', pathSegments[1], 'save.py');
    } else if (pathSegments[2] === 'ask') {
      // Handle the Gemini ask endpoint
      pythonFile = path.join(__dirname, 'api', pathSegments[1], 'ask.py');
    } else {
      // Endpoint with a parameter (e.g., /api/questionnaire/123)
      pythonFile = path.join(__dirname, 'api', pathSegments[1], '[file_id].py');
      pythonArgs = [pathSegments[2]];
    }
  } else if (pathSegments.length === 4) {
    // API endpoint with three segments (e.g., /api/responses/download/123)
    pythonFile = path.join(__dirname, 'api', pathSegments[1], pathSegments[2], '[file_id].py');
    pythonArgs = [pathSegments[3]];
  }
  
  // Collect request body for POST requests
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  
  req.on('end', async () => {
    // Special handling for Vercel serverless functions
    if (pathSegments.length === 2 && SERVERLESS_FUNCTION_ENDPOINTS.includes(pathSegments[1])) {
      const functionName = pathSegments[1];
      console.log(`${colors.cyan}Handling ${functionName} API request${colors.reset}`);
      console.log(`${colors.cyan}Request method:${colors.reset} ${req.method}`);
      console.log(`${colors.cyan}Content-Type:${colors.reset} ${req.headers['content-type']}`);
      console.log(`${colors.cyan}Request body:${colors.reset} ${body}`);
      
      // Use the Vercel serverless function approach
      const jsFile = path.join(__dirname, 'api', `${functionName}.js`);
      
      if (!fs.existsSync(jsFile)) {
        console.log(`${colors.red}JS file not found:${colors.reset} ${jsFile}`);
        res.writeHead(404);
        res.end(JSON.stringify({ error: `${functionName} API endpoint not found` }));
        return;
      }
      
      console.log(`${colors.green}Loading serverless function:${colors.reset} ${functionName}`);
      console.log(`${colors.green}Full path:${colors.reset} ${jsFile}`);
      console.log(`${colors.green}Environment variables:${colors.reset}`, Object.keys(process.env).filter(key => key.includes('API_KEY') || key.includes('api_key')).join(', '));
      
      console.log(`${colors.green}JS file found:${colors.reset} ${jsFile}`);
      
      try {
        // Parse the body if it exists and is JSON
        let parsedBody = {};
        if (body && req.headers['content-type'] === 'application/json') {
          try {
            parsedBody = JSON.parse(body);
            console.log(`${colors.green}Parsed request body:${colors.reset}`, parsedBody);
          } catch (e) {
            console.error(`${colors.red}Error parsing request body:${colors.reset}`, e);
            res.statusCode = 400;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({ error: 'Invalid JSON in request body' }));
            return;
          }
        } else {
          console.log(`${colors.yellow}No JSON body found or content-type is not application/json${colors.reset}`);
          if (req.method === 'POST') {
            res.statusCode = 400;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({ error: 'Content-Type must be application/json for POST requests' }));
            return;
          }
        }
        
        // Create mock req and res objects for the Vercel serverless function
        const mockReq = {
          method: req.method,
          headers: req.headers,
          body: parsedBody,
          url: req.url
        };
        
        console.log(`${colors.cyan}Created mock request:${colors.reset}`, {
          method: mockReq.method,
          url: mockReq.url,
          bodyKeys: Object.keys(mockReq.body || {})
        });
        
        const mockRes = {
          status: (statusCode) => {
            console.log(`${colors.cyan}Setting status code:${colors.reset} ${statusCode}`);
            res.statusCode = statusCode;
            return {
              json: (data) => {
                console.log(`${colors.green}Sending JSON response:${colors.reset}`, data);
                res.setHeader('Content-Type', 'application/json');
                res.end(JSON.stringify(data));
              },
              end: () => {
                console.log(`${colors.yellow}Ending response without data${colors.reset}`);
                res.end();
              }
            };
          },
          setHeader: (name, value) => {
            console.log(`${colors.cyan}Setting header:${colors.reset} ${name}: ${value}`);
            res.setHeader(name, value);
          }
        };
        
        // Load and execute the Vercel serverless function
        console.log(`${colors.green}Loading Vercel serverless function...${colors.reset}`);
        const serverlessFunction = require(jsFile);
        console.log(`${colors.green}Executing Vercel serverless function...${colors.reset}`);
        await serverlessFunction(mockReq, mockRes);
        console.log(`${colors.green}Vercel serverless function executed successfully${colors.reset}`);
      } catch (error) {
        console.error(`${colors.red}Error executing Vercel serverless function:${colors.reset}`, error);
        console.error(`${colors.red}Error stack:${colors.reset}`, error.stack);
        res.statusCode = 500;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({ 
          error: error.message || 'Internal server error',
          stack: error.stack
        }));
      }
      
      return;
    }
    
    // For serverless function endpoints, we don't need to check for Python files
    if (pathSegments.length === 2 && SERVERLESS_FUNCTION_ENDPOINTS.includes(pathSegments[1])) {
      // Skip Python file check for serverless functions
      // These will be handled in the req.on('end') handler
      return;
    }
    
    // Continue with normal Python-based API handling
    // Check if the Python file exists
    if (!fs.existsSync(pythonFile)) {
      console.log(`${colors.red}Python file not found:${colors.reset} ${pythonFile}`);
      res.writeHead(404);
      res.end(JSON.stringify({ error: 'API endpoint not found' }));
      return;
    }
    
    console.log(`${colors.green}Executing Python file:${colors.reset} ${pythonFile}`);
    
    // Continue with normal Python-based API handling
    // Set environment variables for the Python process
    const env = {
      ...process.env,
      REQUEST_METHOD: req.method,
      QUERY_STRING: url.search.substring(1),
      CONTENT_TYPE: req.headers['content-type'] || '',
      HTTP_CONTENT_TYPE: req.headers['content-type'] || '',
      PYTHONPATH: __dirname
    };
    
    if (body) {
      env.BODY = body;
    }
    
    // Execute the Python file
    const python = spawn('python', [pythonFile, ...pythonArgs], { env });
    
    let output = '';
    let errorOutput = '';
    
    python.stdout.on('data', data => {
      output += data.toString();
    });
    
    python.stderr.on('data', data => {
      errorOutput += data.toString();
      console.error(`${colors.red}Python error:${colors.reset} ${data.toString()}`);
    });
    
    python.on('close', code => {
      if (code !== 0) {
        console.error(`${colors.red}Python process exited with code ${code}${colors.reset}`);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Internal server error', details: errorOutput }));
        return;
      }
      
      // Parse the output to extract headers and body
      const parts = output.split('\n\n');
      const headers = parts[0].split('\n');
      const responseBody = parts.slice(1).join('\n\n');
      
      // Set response headers
      let statusCode = 200;
      headers.forEach(header => {
        if (header.startsWith('Status: ')) {
          statusCode = parseInt(header.substring(8), 10);
        } else if (header.includes(':')) {
          const [name, value] = header.split(': ');
          res.setHeader(name, value);
        }
      });
      
      res.writeHead(statusCode);
      res.end(responseBody);
    });
  });
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`${colors.green}API test server running at http://localhost:${PORT}${colors.reset}`);
  console.log(`${colors.blue}Try accessing:${colors.reset} http://localhost:${PORT}/api`);
  console.log(`${colors.yellow}Press Ctrl+C to stop the server${colors.reset}`);
});
